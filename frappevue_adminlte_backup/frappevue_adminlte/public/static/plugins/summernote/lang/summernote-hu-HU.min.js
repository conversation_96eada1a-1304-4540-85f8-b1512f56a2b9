/*! For license information please see summernote-hu-HU.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var r=t();for(var o in r)("object"==typeof exports?exports:e)[o]=r[o]}}(window,(function(){return function(e){var t={};function r(o){if(t[o])return t[o].exports;var s=t[o]={i:o,l:!1,exports:{}};return e[o].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(o,s,function(t){return e[t]}.bind(null,s));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=23)}({23:function(e,t){var r;(r=jQuery).extend(r.summernote.lang,{"hu-HU":{font:{bold:"Félkövér",italic:"Dőlt",underline:"Aláhúzott",clear:"Formázás törlése",height:"Sorköz",name:"Betűtípus",strikethrough:"Áthúzott",subscript:"Subscript",superscript:"Superscript",size:"Betűméret"},image:{image:"Kép",insert:"Kép beszúrása",resizeFull:"Átméretezés teljes méretre",resizeHalf:"Átméretezés felére",resizeQuarter:"Átméretezés negyedére",floatLeft:"Igazítás balra",floatRight:"Igazítás jobbra",floatNone:"Igazítás törlése",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Ide húzhat képet vagy szöveget",dropImage:"Engedje el a képet vagy szöveget",selectFromFiles:"Fájlok kiválasztása",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Kép URL címe",remove:"Kép törlése",original:"Original"},video:{video:"Videó",videoLink:"Videó hivatkozás",insert:"Videó beszúrása",url:"Videó URL címe",providers:"(YouTube, Vimeo, Vine, Instagram, DailyMotion vagy Youku)"},link:{link:"Hivatkozás",insert:"Hivatkozás beszúrása",unlink:"Hivatkozás megszüntetése",edit:"Szerkesztés",textToDisplay:"Megjelenítendő szöveg",url:"Milyen URL címre hivatkozzon?",openInNewWindow:"Megnyitás új ablakban"},table:{table:"Táblázat",addRowAbove:"Add row above",addRowBelow:"Add row below",addColLeft:"Add column left",addColRight:"Add column right",delRow:"Delete row",delCol:"Delete column",delTable:"Delete table"},hr:{insert:"Elválasztó vonal beszúrása"},style:{style:"Stílus",p:"Normál",blockquote:"Idézet",pre:"Kód",h1:"Fejléc 1",h2:"Fejléc 2",h3:"Fejléc 3",h4:"Fejléc 4",h5:"Fejléc 5",h6:"Fejléc 6"},lists:{unordered:"Listajeles lista",ordered:"Számozott lista"},options:{help:"Súgó",fullscreen:"Teljes képernyő",codeview:"Kód nézet"},paragraph:{paragraph:"Bekezdés",outdent:"Behúzás csökkentése",indent:"Behúzás növelése",left:"Igazítás balra",center:"Igazítás középre",right:"Igazítás jobbra",justify:"Sorkizárt"},color:{recent:"Jelenlegi szín",more:"További színek",background:"Háttérszín",foreground:"Betűszín",transparent:"Átlátszó",setTransparent:"Átlászóság beállítása",reset:"Visszaállítás",resetToDefault:"Alaphelyzetbe állítás"},shortcut:{shortcuts:"Gyorsbillentyű",close:"Bezárás",textFormatting:"Szöveg formázása",action:"Művelet",paragraphFormatting:"Bekezdés formázása",documentStyle:"Dokumentumstílus",extraKeys:"Extra keys"},help:{insertParagraph:"Új bekezdés",undo:"Visszavonás",redo:"Újra",tab:"Behúzás növelése",untab:"Behúzás csökkentése",bold:"Félkövérre állítás",italic:"Dőltre állítás",underline:"Aláhúzás",strikethrough:"Áthúzás",removeFormat:"Formázás törlése",justifyLeft:"Balra igazítás",justifyCenter:"Középre igazítás",justifyRight:"Jobbra igazítás",justifyFull:"Sorkizárt",insertUnorderedList:"Számozatlan lista be/ki",insertOrderedList:"Számozott lista be/ki",outdent:"Jelenlegi bekezdés behúzásának megszüntetése",indent:"Jelenlegi bekezdés behúzása",formatPara:"Blokk formázása bekezdésként (P tag)",formatH1:"Blokk formázása, mint Fejléc 1",formatH2:"Blokk formázása, mint Fejléc 2",formatH3:"Blokk formázása, mint Fejléc 3",formatH4:"Blokk formázása, mint Fejléc 4",formatH5:"Blokk formázása, mint Fejléc 5",formatH6:"Blokk formázása, mint Fejléc 6",insertHorizontalRule:"Vízszintes vonal beszúrása","linkDialog.show":"Link párbeszédablak megjelenítése"},history:{undo:"Visszavonás",redo:"Újra"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"}}})}})}));