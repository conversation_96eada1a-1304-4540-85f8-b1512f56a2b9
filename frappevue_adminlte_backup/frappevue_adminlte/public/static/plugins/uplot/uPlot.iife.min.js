/*! https://github.com/leeoniya/uPlot (v1.4.4) */
var uPlot=function(){"use strict";function n(n,t,e,r){var i;e=e||0;for(var o=2147483647>=(r=r||t.length-1);r-e>1;)n>t[i=o?e+r>>1:v((e+r)/2)]?e=i:r=i;return n-t[e]>t[r]-n?r:e}var t=[0,0];function e(n,e,r,i){return t[0]=0>r?D(n,-r):n,t[1]=0>i?D(e,-i):e,t}function r(n,t,r,i){var o,l,a,u=10==r?w:x;return n==t&&(n/=r,t*=r),i?(o=v(u(n)),l=m(u(t)),n=(a=e(g(r,o),g(r,l),o,l))[0],t=a[1]):(o=v(u(n)),l=v(u(t)),n=z(n,(a=e(g(r,o),g(r,l),o,l))[0]),t=T(t,a[1])),[n,t]}var i={pad:0,soft:null,mode:0},o={min:i,max:i};function l(n,t,e,r){return H(e)?u(n,t,e):(i.pad=e,i.soft=r?0:null,i.mode=r?2:0,u(n,t,o))}function a(n,t){return null==n?t:n}function u(n,t,e){var r=e.min,i=e.max,o=a(r.pad,0),l=a(i.pad,0),u=a(r.hard,-b),s=a(i.hard,b),f=a(r.soft,b),h=a(i.soft,-b),m=a(r.mode,0),x=a(i.mode,0),_=t-n,y=_||c(t)||1e3,M=w(y),k=g(10,v(M)),S=D(z(n-y*(0==_?0==n?.1:1:o),k/100),6),E=n>=f&&(1==m||2==m&&f>S)?f:b,W=p(u,E>S&&n>=E?E:d(E,S)),Y=D(T(t+y*(0==_?0==t?.1:1:l),k/100),6),C=h>=t&&(1==x||2==x&&Y>h)?h:-b,A=d(s,Y>C&&C>=t?C:p(C,Y));return W==A&&0==W&&(A=100),[W,A]}var s=new Intl.NumberFormat(navigator.language).format,f=Math,c=f.abs,v=f.floor,h=f.round,m=f.ceil,d=f.min,p=f.max,g=f.pow,w=f.log10,x=f.log2,_=f.PI,b=1/0;function y(n,t){return h(n/t)*t}function M(n,t,e){return d(p(n,t),e)}function k(n){return"function"==typeof n?n:function(){return n}}function S(n,t){return t}function T(n,t){return m(n/t)*t}function z(n,t){return v(n/t)*t}function D(n,t){return h(n*(t=Math.pow(10,t)))/t}var E=new Map;function W(n){return((""+n).split(".")[1]||"").length}function Y(n,t,e,r){for(var i=[],o=r.map(W),l=t;e>l;l++)for(var a=c(l),u=D(g(n,l),a),s=0;r.length>s;s++){var f=r[s]*u,v=(0>f||0>l?a:0)+(o[s]>l?o[s]:0),h=D(f,v);i.push(h),E.set(h,v)}return i}var C={},A=Array.isArray;function F(n){return"string"==typeof n}function H(n){return"object"==typeof n&&null!==n}function P(n){var t;if(A(n))t=n.map(P);else if(H(n))for(var e in t={},n)t[e]=P(n[e]);else t=n;return t}function N(n){for(var t=arguments,e=1;t.length>e;e++){var r=t[e];for(var i in r)H(n[i])?N(n[i],P(r[i])):n[i]=P(r[i])}return n}var I="undefined"==typeof queueMicrotask?function(n){return Promise.resolve().then(n)}:queueMicrotask,V="width",L="height",O="top",j="bottom",B="left",G="right",R="#000",U="#0000",J="mousemove",q="mousedown",Z="mouseup",X="mouseenter",K="mouseleave",Q="dblclick",$="resize",nn="scroll",tn="u-off",en="u-label",rn=requestAnimationFrame,on=document,ln=window,an=devicePixelRatio;function un(n,t){null!=t&&n.classList.add(t)}function sn(n,t){n.classList.remove(t)}function fn(n,t,e){n.style[t]=e+"px"}function cn(n,t,e,r){var i=on.createElement(n);return null!=t&&un(i,t),null!=e&&e.insertBefore(i,r),i}function vn(n,t){return cn("div",n,t)}function hn(n,t,e,r,i){n.style.transform="translate("+t+"px,"+e+"px)",0>t||0>e||t>r||e>i?un(n,tn):sn(n,tn)}var mn={passive:!0};function dn(n,t,e){t.addEventListener(n,e,mn)}function pn(n,t,e){t.removeEventListener(n,e,mn)}var gn=["January","February","March","April","May","June","July","August","September","October","November","December"],wn=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function xn(n){return n.slice(0,3)}var _n=wn.map(xn),bn=gn.map(xn),yn={MMMM:gn,MMM:bn,WWWW:wn,WWW:_n};function Mn(n){return(10>n?"0":"")+n}var kn={YYYY:function(n){return n.getFullYear()},YY:function(n){return(n.getFullYear()+"").slice(2)},MMMM:function(n,t){return t.MMMM[n.getMonth()]},MMM:function(n,t){return t.MMM[n.getMonth()]},MM:function(n){return Mn(n.getMonth()+1)},M:function(n){return n.getMonth()+1},DD:function(n){return Mn(n.getDate())},D:function(n){return n.getDate()},WWWW:function(n,t){return t.WWWW[n.getDay()]},WWW:function(n,t){return t.WWW[n.getDay()]},HH:function(n){return Mn(n.getHours())},H:function(n){return n.getHours()},h:function(n){var t=n.getHours();return 0==t?12:t>12?t-12:t},AA:function(n){return 12>n.getHours()?"AM":"PM"},aa:function(n){return 12>n.getHours()?"am":"pm"},a:function(n){return 12>n.getHours()?"a":"p"},mm:function(n){return Mn(n.getMinutes())},m:function(n){return n.getMinutes()},ss:function(n){return Mn(n.getSeconds())},s:function(n){return n.getSeconds()},fff:function(n){return function(n){return(10>n?"00":100>n?"0":"")+n}(n.getMilliseconds())}};function Sn(n,t){t=t||yn;for(var e,r=[],i=/\{([a-z]+)\}|[^{]+/gi;e=i.exec(n);)r.push("{"==e[0][0]?kn[e[1]]:e[0]);return function(n){for(var e="",i=0;r.length>i;i++)e+="string"==typeof r[i]?r[i]:r[i](n,t);return e}}var Tn=(new Intl.DateTimeFormat).resolvedOptions().timeZone,zn=function(n){return n%1==0},Dn=[1,2,2.5,5],En=Dn.filter(zn),Wn=Y(10,-16,0,Dn),Yn=Y(10,0,16,Dn),Cn=Yn.filter(zn),An=Wn.concat(Yn),Fn=3600,Hn=24*Fn,Pn=30*Hn,Nn=365*Hn,In=Y(10,-3,0,En).concat([1,5,10,15,30,60,300,600,900,1800,Fn,2*Fn,3*Fn,4*Fn,6*Fn,8*Fn,12*Fn,Hn,2*Hn,3*Hn,4*Hn,5*Hn,6*Hn,7*Hn,8*Hn,9*Hn,10*Hn,15*Hn,Pn,2*Pn,3*Pn,4*Pn,6*Pn,Nn,2*Nn,5*Nn,10*Nn,25*Nn,50*Nn,100*Nn]);function Vn(n,t){return n.map((function(n){return n.map((function(e,r){return 0==r||8==r||null==e?e:t(1==r||0==n[8]?e:n[1]+e)}))}))}Y(2,-53,53,[1]);var Ln="{YYYY}",On="\n"+Ln,jn="{M}/{D}",Bn="\n"+jn,Gn=Bn+"/{YY}",Rn="{aa}",Un="{h}:{mm}"+Rn,Jn="\n"+Un,qn=":{ss}",Zn=null,Xn=[[Nn,Ln,Zn,Zn,Zn,Zn,Zn,Zn,1],[28*Hn,"{MMM}",On,Zn,Zn,Zn,Zn,Zn,1],[Hn,jn,On,Zn,Zn,Zn,Zn,Zn,1],[Fn,"{h}"+Rn,Gn,Zn,Bn,Zn,Zn,Zn,1],[60,Un,Gn,Zn,Bn,Zn,Zn,Zn,1],[1,qn,Gn+" "+Un,Zn,Bn+" "+Un,Zn,Jn,Zn,1],[.001,qn+".{fff}",Gn+" "+Un,Zn,Bn+" "+Un,Zn,Jn,Zn,1]];function Kn(n,t){return function(e,r,i,o,l){var a,u,s,f,c,v,h=t.find((function(n){return l>=n[0]}))||t[t.length-1];return r.map((function(t){var e=n(t),r=e.getFullYear(),i=e.getMonth(),o=e.getDate(),l=e.getHours(),m=e.getMinutes(),d=e.getSeconds(),p=r!=a&&h[2]||i!=u&&h[3]||o!=s&&h[4]||l!=f&&h[5]||m!=c&&h[6]||d!=v&&h[7]||h[1];return a=r,u=i,s=o,f=l,c=m,v=d,p(e)}))}}function Qn(n,t,e){return new Date(n,t,e)}function $n(n,t){return t(n)}function nt(n,t){return function(e,r){return t(n(r))}}var tt=[0,0];function et(n,t,e){return function(n){0==n.button&&e(n)}}function rt(n,t,e){return e}var it={show:!0,x:!0,y:!0,lock:!1,move:function(n,t,e){return tt[0]=t,tt[1]=e,tt},points:{show:function(n,t){var e=n.series[t],r=vn();r.style.background=e.stroke||R;var i=bt(e.width,1),o=(i-1)/-2;return fn(r,V,i),fn(r,L,i),fn(r,"marginLeft",o),fn(r,"marginTop",o),r}},bind:{mousedown:et,mouseup:et,click:et,dblclick:et,mousemove:rt,mouseleave:rt,mouseenter:rt},drag:{setScale:!0,x:!0,y:!1,dist:0,uni:null,_x:!1,_y:!1},focus:{prox:-1},left:-10,top:-10,idx:null,dataIdx:function(n,t,e){return e}},ot={show:!0,stroke:"rgba(0,0,0,0.07)",width:2,filter:S},lt=N({},ot,{size:10}),at='12px system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',ut="bold "+at,st={show:!0,scale:"x",space:50,gap:5,size:50,labelSize:30,labelFont:ut,side:2,grid:ot,ticks:lt,font:at,rotate:0},ft={show:!0,scale:"x",auto:!1,sorted:1,min:b,max:-b,idxs:[]};function ct(n,t){return t.map((function(n){return null==n?"":s(n)}))}function vt(n,t,e,r,i,o,l){for(var a=[],u=E.get(i)||0,s=e=l?e:D(T(e,i),u);r>=s;s=D(s+i,u))a.push(Object.is(s,-0)?0:s);return a}function ht(n,t,e,r,i){var o=[],l=n.scales[n.axes[t].scale].log,a=v((10==l?w:x)(e));i=g(l,a),0>a&&(i=D(i,-a));var u=e;do{o.push(u),i*l>(u=D(u+i,E.get(i)))||(i=u)}while(r>=u);return o}var mt=/./,dt=/[12357]/,pt=/[125]/,gt=/1/;function wt(n,t,e){var r=n.axes[e],i=r.scale;if(2==n.scales[i].log)return t;var o=n.valToPos,l=r._space,a=o(10,i),u=o(9,i)-a<l?o(7,i)-a<l?o(5,i)-a<l?gt:pt:dt:mt;return t.map((function(n){return u.test(n)?n:null}))}function xt(n,t){return null==t?"":s(t)}var _t={show:!0,scale:"y",space:30,gap:5,size:50,labelSize:30,labelFont:ut,side:3,grid:ot,ticks:lt,font:at,rotate:0};function bt(n,t){return D((3+2*(n||1))*t,3)}function yt(n,t){var e=n.scales[n.series[t].scale];return 3==e.distr?e.min:0}var Mt={scale:"y",auto:!0,sorted:0,show:!0,band:!1,spanGaps:!1,isGap:function(){return!0},alpha:1,points:{show:function(n,t){var e=n.series[t],r=(bt(e.width,an),n.series[0].idxs);return n.bbox.width/(e.points.space*an)>=r[1]-r[0]}},values:null,min:b,max:-b,idxs:[],path:null,clip:null},kt={time:!0,auto:!0,distr:1,log:10,min:null,max:null},St=N({},kt,{time:!1}),Tt={};function zt(){var n=[];return{sub:function(t){n.push(t)},unsub:function(t){n=n.filter((function(n){return n!=t}))},pub:function(t,e,r,i,o,l,a){n.length>1&&n.forEach((function(n){n!=e&&n.pub(t,e,r,i,o,l,a)}))}}}function Dt(n,t,e,r){return(r?[n[0],n[1]].concat(n.slice(2)):[n[0]].concat(n.slice(1))).map((function(n,r){return Et(n,r,t,e)}))}function Et(n,t,e,r){return N({},0==t||n&&n.side%2==0?e:r,n)}function Wt(n,t){return 3==t.distr?w(n/t.min)/w(t.max/t.min):(n-t.min)/(t.max-t.min)}function Yt(n,t,e,r){return r+(1-Wt(n,t))*e}function Ct(n,t,e,r){return r+Wt(n,t)*e}var At=[null,null];function Ft(n,t,e){return null==t?At:[t,e]}var Ht=Ft;function Pt(n,t,e){return null==t?At:l(t,e,.1,!0)}function Nt(n,t,e,i){return null==t?At:r(t,e,n.scales[i].log,!1)}var It=Nt;function Vt(n){var t;return[n=n.replace(/(\d+)px/,(function(n,e){return(t=h(e*an))+"px"})),t]}function Lt(t,e,i){var o={},a=!1;o.status=0;var u=o.root=vn("uplot");null!=t.id&&(u.id=t.id),un(u,t.class),t.title&&(vn("u-title",u).textContent=t.title);var s=cn("canvas"),f=o.ctx=s.getContext("2d"),m=vn("u-wrap",u),x=vn("u-under",m);m.appendChild(s);var z=vn("u-over",m);((t=P(t)).plugins||[]).forEach((function(n){n.opts&&(t=n.opts(o,t)||t)}));var W=o.series=Dt(t.series||[],ft,Mt,!1),Y=o.axes=Dt(t.axes||[],st,_t,!0),mn=o.scales={},gn=W[0].scale;function wn(n){var e=mn[n];if(null==e){var r=(t.scales||C)[n]||C;if(null!=r.from)wn(r.from),mn[n]=N({},mn[r.from],r);else{var i=(e=mn[n]=N({},n==gn?kt:St,r)).time,o=3==e.distr,a=e.range;if(n!=gn&&!A(a)&&H(a)){var u=a;a=function(n,t,e){return null==t?At:l(t,e,u)}}e.range=k(a||(i?Ht:n==gn?o?It:Ft:o?Nt:Pt)),e.auto=k(e.auto)}}}for(var xn in wn("x"),wn("y"),W.forEach((function(n){wn(n.scale)})),t.scales)wn(xn);var _n=mn[gn].distr,bn={};for(var yn in mn){var Mn=mn[yn];null==Mn.min&&null==Mn.max||(bn[yn]={min:Mn.min,max:Mn.max})}var kn=o.gutters=N({x:h(_t.size/2),y:h(st.size/3),_x:null,_y:null},t.gutters);kn.x=k(kn.x),kn.y=k(kn.y),kn._x=kn.x(o),kn._y=kn.y(o);var Tn,zn,Dn=t.tzDate||function(n){return new Date(1e3*n)},En=t.fmtDate||Sn,Wn=function(n){return function(t,e,r,i,o,l){var a=[],u=o>=Nn,s=o>=Pn&&Nn>o,f=n(r),c=f/1e3,h=Qn(f.getFullYear(),u?0:f.getMonth(),s||u?1:f.getDate()),m=h/1e3;if(s||u)for(var d=s?o/Pn:0,p=u?o/Nn:0,g=c==m?c:Qn(h.getFullYear()+p,h.getMonth()+d,1)/1e3,w=new Date(1e3*g),x=w.getFullYear(),_=w.getMonth(),b=0;i>=g;b++){var y=Qn(x+p*b,_+d*b,1);(g=(+y+(y-n(y/1e3)))/1e3)>i||a.push(g)}else{var M=Hn>o?o:Hn,k=m+(v(r)-v(c))+T(c-m,M);a.push(k);for(var S=n(k),z=S.getHours()+S.getMinutes()/60+S.getSeconds()/Fn,E=o/Fn,W=l/t.axes[e]._space;(k=D(k+o,3))<=i;)if(E>1){var Y=v(D(z+E,6))%24,C=n(k).getHours()-Y;C>1&&(C=-1),z=(z+E)%24,.7>D(((k-=C*Fn)-a[a.length-1])/o,3)*W||a.push(k)}else a.push(k)}return a}}(Dn),Yn=Kn(Dn,Vn(Xn,En)),Ln=nt(Dn,$n("{YYYY}-{MM}-{DD} {h}:{mm}{aa}",En)),On=N({show:!0,live:!0},t.legend),jn=On.show,Bn=[],Gn=!1;if(jn){Tn=cn("table","u-legend",u);var Rn=W[1]?W[1].values:null;if(Gn=null!=Rn){var Un=cn("tr","u-thead",Tn);for(var Jn in cn("th",null,Un),zn=Rn(o,1,0))cn("th",en,Un).textContent=Jn}else zn={_:0},un(Tn,"u-inline"),On.live&&un(Tn,"u-live")}var qn=new Map;function Zn(n,t,e){var r=qn.get(t)||{},i=Rt.bind[n](o,t,e);i&&(dn(n,t,r[n]=i),qn.set(t,r))}var tt=0,et=0,rt=0,ot=0,lt=0,at=0,ut=0,mt=0,dt=0,pt=0;o.bbox={};var gt=!1,Wt=!1,Lt=!1,Ot=!1,jt=!1;function Bt(n,t){n==o.width&&t==o.height||Gt(n,t),he(!1),Lt=!0,Wt=!0,Ot=!0,jt=!0,Se()}function Gt(n,t){o.width=tt=rt=n,o.height=et=ot=t,lt=at=0,function(){var n=!1,t=!1,e=!1,r=!1;Y.forEach((function(i){if(i.show&&i._show){var o=i.side,l=o%2,a=i._size+(i.labelSize=null!=i.label?i.labelSize||30:0);a>0&&(l?(rt-=a,3==o?(lt+=a,r=!0):e=!0):(ot-=a,0==o?(at+=a,n=!0):t=!0))}})),(n||t)&&(e||(rt-=kn._x),r||(rt-=kn._x,lt+=kn._x)),(r||e)&&(t||(ot-=kn._y),n||(ot-=kn._y,at+=kn._y))}(),function(){var n=lt+rt,t=at+ot,e=lt,r=at;function i(i,o){switch(i){case 1:return(n+=o)-o;case 2:return(t+=o)-o;case 3:return(e-=o)+o;case 0:return(r-=o)+o}}Y.forEach((function(n){if(n.show&&n._show){var t=n.side;n._pos=i(t,n._size),null!=n.label&&(n._lpos=i(t,n.labelSize))}}))}();var e=o.bbox;ut=e.left=y(lt*an,.5),mt=e.top=y(at*an,.5),dt=e.width=y(rt*an,.5),pt=e.height=y(ot*an,.5)}o.setSize=function(n){Bt(n.width,n.height)};var Rt=o.cursor=N({},it,t.cursor);Rt._lock=!1,Rt.points.show=k(Rt.points.show);var Ut,Jt,qt=o.focus=N({},t.focus||{alpha:.3},Rt.focus),Zt=qt.prox>=0,Xt=[null];function Kt(n,t){var e=mn[n.scale].time,r=n.value;if(n.value=e?F(r)?nt(Dn,$n(r,En)):r||Ln:r||xt,n.label=n.label||(e?"Time":"Value"),t>0){n.width=null==n.width?1:n.width,n.paths=n.paths||se,n.fillTo=n.fillTo||yt;var i=bt(n.width,1);n.points=N({},{size:i,width:p(1,.2*i),stroke:n.stroke,space:2*i},n.points),n.points.show=k(n.points.show),n._paths=null}if(jn&&Bn.splice(t,0,function(n,t){if(0==t&&(Gn||!On.live))return null;var e=[],r=cn("tr","u-series",Tn,Tn.childNodes[t]);un(r,n.class),n.show||un(r,tn);var i=cn("th",null,r),o=vn("u-marker",i);o.style.borderColor=n.width?n.stroke:t>0&&n.points.width?n.points.stroke:null,o.style.backgroundColor=n.fill||null;var l=vn(en,i);for(var a in l.textContent=n.label,t>0&&(Zn("click",i,(function(){Rt._lock||Le(W.indexOf(n),{show:!n.show},ir.setSeries)})),Zt&&Zn(X,i,(function(){Rt._lock||Le(W.indexOf(n),{focus:!0},ir.setSeries)}))),zn){var u=cn("td","u-value",r);u.textContent="--",e.push(u)}return e}(n,t)),Rt.show){var l=function(n,t){if(t>0){var e=Rt.points.show(o,t);if(e)return un(e,"u-cursor-pt"),un(e,n.class),hn(e,-10,-10,rt,ot),z.insertBefore(e,Xt[t]),e}}(n,t);l&&Xt.splice(t,0,l)}}o.addSeries=function(n,t){n=Et(n,t=null==t?W.length:t,ft,Mt),W.splice(t,0,n),Kt(W[t],t)},o.delSeries=function(n){W.splice(n,1),jn&&Bn.splice(n,1)[0][0].parentNode.remove(),Xt.length>1&&Xt.splice(n,1)[0].remove()},W.forEach(Kt),Y.forEach((function(n,t){if(n._show=n.show,n.show){var e=mn[n.scale];null==e&&(n.scale=n.side%2?W[1].scale:gn,e=mn[n.scale]);var r=e.time;n.size=k(n.size),n.space=k(n.space),n.rotate=k(n.rotate),n.incrs=k(n.incrs||(2==e.distr?Cn:r?In:An)),n.splits=k(n.splits||(r&&1==e.distr?Wn:3==e.distr?ht:vt));var i=n.values;n.values=r?A(i)?Kn(Dn,Vn(i,En)):F(i)?function(n,t){var e=Sn(t);return function(t,r){return r.map((function(t){return e(n(t))}))}}(Dn,i):i||Yn:i||ct,n.filter=k(n.filter||(3==e.distr?wt:S)),n.font=Vt(n.font),n.labelFont=Vt(n.labelFont),n._size=n.size(o,null,t),n._space=n._rotate=n._incrs=n._found=n._splits=n._values=null}}));var Qt=null,$t=null,ne=W[0].idxs,te=null,ee=!1;function re(n,t){if(!A(n)&&H(n)&&(Jt=n.isGap,n=n.data),(n=n||[])[0]=n[0]||[],o.data=n,e=n.slice(),Ut=(te=e[0]).length,2==_n&&(e[0]=te.map((function(n,t){return t}))),he(!0),rr("setData"),!1!==t){var r=mn[gn];r.auto(o,ee)?ie():Ve(gn,r.min,r.max),Ot=!0,jt=!0,Se()}}function ie(){var n,t,i,o;ee=!0,Ut>0?(Qt=ne[0]=0,$t=ne[1]=Ut-1,i=e[0][Qt],o=e[0][$t],2==_n?(i=Qt,o=$t):1==Ut&&(3==_n?(i=(n=r(i,i,mn[gn].log,!1))[0],o=n[1]):mn[gn].time?o=i+86400:(i=(t=l(i,o,.1,!0))[0],o=t[1]))):(Qt=ne[0]=i=null,$t=ne[1]=o=null),Ve(gn,i,o),ee=!1}function oe(n,t,e,r){f.strokeStyle=n||U,f.lineWidth=t,f.lineJoin="round",f.setLineDash(e||[]),f.fillStyle=r||U}o.setData=re;var le=1;function ae(n,t,e){if(e>t){var r=n[n.length-1];r&&r[0]==t?r[1]=e:n.push([t,e])}}function ue(n,t,e,r){for(var i=1==r?t:e;i>=t&&e>=i;i+=r)if(null!=n[i])return i;return-1}function se(n,t,r,i){var o,l,a,u,s,f=W[t],c=Jt||f.isGap,v=e[0],m=e[t],g=mn[gn],w=mn[f.scale],x=1==le?{stroke:new Path2D,fill:null,clip:null}:W[t-1]._paths,_=x.stroke,M=D(f.width*an,3),k=b,S=-b,T=[],z=h(Ct(v[1==le?r:i],g,dt,ut)),E=!1,Y=ue(m,r,i,1),C=ue(m,r,i,-1),A=y(Ct(v[Y],g,dt,ut),.5),F=y(Ct(v[C],g,dt,ut),.5);A>ut&&ae(T,ut,A),f.band&&1==le&&_.lineTo(A-2*M,h(Yt(m[r],w,pt,mt)));for(var H=1==le?r:i;H>=r&&i>=H;H+=le){var P=h(Ct(v[H],g,dt,ut));if(P==z)null!=m[H]?(o=h(Yt(m[H],w,pt,mt)),k=d(o,k),S=p(o,S)):!E&&c(n,t,H)&&(E=!0);else{var N=!1;k!=b?(_.lineTo(z,k),_.lineTo(z,S),_.lineTo(z,o),l=z):E&&(N=!0,E=!1),null!=m[H]?(o=h(Yt(m[H],w,pt,mt)),_.lineTo(P,o),k=S=o,P-z>1&&null==m[H-1]&&c(n,t,H-1)&&(N=!0)):(k=b,S=-b,!E&&c(n,t,H)&&(E=!0)),N&&ae(T,l,P),z=P}}if(ut+dt>F&&ae(T,F,ut+dt),f.band&&(1==le?(a=F+2*M,u=C,s=e[t+1]):(a=A-2*M,u=Y,s=e[t-1]),_.lineTo(a,h(Yt(m[u],w,pt,mt))),_.lineTo(a,h(Yt(s[u],w,pt,mt)))),1==le&&(x.clip=function(n,t){var e=null;if(t.length>0&&!W[n].spanGaps){e=new Path2D;for(var r=ut,i=0;t.length>i;i++){var o=t[i];e.rect(r,mt,o[0]-r,mt+pt),r=o[1]}e.rect(r,mt,ut+dt-r,mt+pt)}return e}(t,T),null!=f.fill)){var I=x.fill=new Path2D(_),V=h(Yt(f.fillTo(n,t,f.min,f.max),w,pt,mt));I.lineTo(F,V),I.lineTo(A,V)}return f.band&&(le*=-1),x}function fe(n,t,e,r,i,o,l,a,u){var s=l%2/2;f.translate(s,s),oe(a,l,u),f.beginPath();var c,v,h,m,d=i+(0==r||3==r?-o:o);0==e?(v=i,m=d):(c=i,h=d),n.forEach((function(n,r){null!=t[r]&&(0==e?c=h=n:v=m=n,f.moveTo(c,v),f.lineTo(h,m))})),f.stroke(),f.translate(-s,-s)}function ce(){var n=!0;return Y.forEach((function(t,e){if(t.show){var r=mn[t.scale];if(null!=r.min){t._show||(n=!1,t._show=!0,he(!1));var i=t.side,l=r.min,a=r.max,u=function(n,t,e,r){var i,l=Y[n];if(r>0){var a=l._space=l.space(o,n,t,e,r),u=l._incrs=l.incrs(o,n,t,e,r,a);i=l._found=function(n,t,e,r,i){for(var o=r/(t-n),l=(""+v(n)).length,a=0;e.length>a;a++){var u=e[a]*o,s=10>e[a]?E.get(e[a]):0;if(u>=i&&17>l+s)return[e[a],u]}return[0,0]}(t,e,u,r,a)}else i=[0,0];return i}(e,l,a,0==i%2?rt:ot),s=u[0],f=u[1];if(0!=f){var c=t._splits=t.splits(o,e,l,a,s,f,2==r.distr),h=2==r.distr?c.map((function(n){return te[n]})):c,m=2==r.distr?te[c[1]]-te[c[0]]:s,d=t._values=t.values(o,t.filter(o,h,e,f,m),e,f,m);t._rotate=2==i?t.rotate(o,d,e,f):0;var p=t._size;t._size=t.size(o,d,e),null!=p&&t._size!=p&&(n=!1)}}else t._show&&(n=!1,t._show=!1,he(!1))}})),n}function ve(){var n=!0,t=kn._x,e=kn._y;return kn._x=kn.x(o),kn._y=kn.y(o),kn._x==t&&kn._y==e||(n=!1),n}function he(n){W.forEach((function(t,e){e>0&&(t._paths=null,n&&(t.min=null,t.max=null))}))}o.paths=se;var me,de,pe,ge,we,xe,_e,be,ye,Me,ke=!1;function Se(){ke||(I(Te),ke=!0)}function Te(){gt&&(function(){var t=P(mn);for(var r in t){var i=t[r],l=bn[r];if(null!=l&&null!=l.min)N(i,l),r==gn&&he(!0);else if(r!=gn)if(0==Ut&&null==i.from){var a=i.range(o,null,null,r);i.min=a[0],i.max=a[1]}else i.min=b,i.max=-b}if(Ut>0)for(var u in W.forEach((function(r,i){var l=r.scale,a=t[l],u=bn[l];if(0==i){var s=a.range(o,a.min,a.max,l);a.min=s[0],a.max=s[1],Qt=n(a.min,e[0]),$t=n(a.max,e[0]),a.min>e[0][Qt]&&Qt++,e[0][$t]>a.max&&$t--,r.min=te[Qt],r.max=te[$t]}else if(r.show&&r.auto&&a.auto(o,ee)&&(null==u||null==u.min)){var f=null==r.min?function(n,t,e,r){var i=b,o=-b;if(1==r)i=n[t],o=n[e];else if(-1==r)i=n[e],o=n[t];else for(var l=t;e>=l;l++)null!=n[l]&&(i=d(i,n[l]),o=p(o,n[l]));return[i,o]}(e[i],Qt,$t,r.sorted):[r.min,r.max];a.min=d(a.min,r.min=f[0]),a.max=p(a.max,r.max=f[1])}r.idxs[0]=Qt,r.idxs[1]=$t})),t){var s=t[u],f=bn[u];if(null==s.from&&(null==f||null==f.min)){var c=s.range(o,s.min==b?null:s.min,s.max==-b?null:s.max,u);s.min=c[0],s.max=c[1]}}for(var v in t){var h=t[v];if(null!=h.from){var m=t[h.from],g=h.range(o,m.min,m.max,v);h.min=g[0],h.max=g[1]}}var w={},x=!1;for(var _ in t){var y=t[_],M=mn[_];M.min==y.min&&M.max==y.max||(M.min=y.min,M.max=y.max,w[_]=x=!0)}if(x){for(var k in W.forEach((function(n){w[n.scale]&&(n._paths=null)})),w)Lt=!0,rr("setScale",k);Rt.show&&(Ot=!0)}for(var S in bn)bn[S]=null}(),gt=!1),Lt&&(function(){for(var n=!1;!n;){var t=ce(),e=ve();(n=t&&e)||(Gt(o.width,o.height),Wt=!0)}}(),Lt=!1),Wt&&(fn(x,B,lt),fn(x,O,at),fn(x,V,rt),fn(x,L,ot),fn(z,B,lt),fn(z,O,at),fn(z,V,rt),fn(z,L,ot),fn(m,V,tt),fn(m,L,et),s.width=h(tt*an),s.height=h(et*an),Ue(),rr("setSize"),Wt=!1),Rt.show&&Ot&&(Ge(),Ot=!1),tt>0&&et>0&&(f.clearRect(0,0,s.width,s.height),rr("drawClear"),function(){Y.forEach((function(n,t){if(n.show&&n._show){var e=mn[n.scale],r=n.side,i=r%2,l=0==i?Ct:Yt,a=0==i?dt:pt,u=0==i?ut:mt,s=h(n.gap*an),c=n.ticks,v=c.show?h(c.size*an):0,m=n._found,d=m[0],p=m[1],g=n._splits,w=2==e.distr?g.map((function(n){return te[n]})):g,x=2==e.distr?te[g[1]]-te[g[0]]:d,b=n._rotate*-_/180,y=h(n._pos*an),M=y+(v+s)*(0==i&&0==r||1==i&&3==r?-1:1),k=0==i?M:0,S=1==i?M:0;f.font=n.font[0],f.fillStyle=n.stroke||R,f.textAlign=1==n.align?B:2==n.align?G:b>0?B:0>b?G:0==i?"center":3==r?G:B,f.textBaseline=b||1==i?"middle":2==r?O:j;var T=1.5*n.font[1],z=g.map((function(n){return h(l(n,e,a,u))}));if(n._values.forEach((function(n,t){null!=n&&(0==i?S=z[t]:k=z[t],(""+n).split(/\n/gm).forEach((function(n,t){b?(f.save(),f.translate(S,k+t*T),f.rotate(b),f.fillText(n,0,0),f.restore()):f.fillText(n,S,k+t*T)})))})),n.label){f.save();var E=h(n._lpos*an);1==i?(S=k=0,f.translate(E,h(mt+pt/2)),f.rotate((3==r?-_:_)/2)):(S=h(ut+dt/2),k=E),f.font=n.labelFont[0],f.textAlign="center",f.textBaseline=2==r?O:j,f.fillText(n.label,S,k),f.restore()}c.show&&fe(z,c.filter(o,w,t,p,x),i,r,y,v,D(c.width*an,3),c.stroke);var W=n.grid;W.show&&fe(z,W.filter(o,w,t,p,x),i,0==i?2:1,0==i?mt:ut,0==i?pt:dt,D(W.width*an,3),W.stroke,W.dash)}})),rr("drawAxes")}(),Ut>0&&function(){W.forEach((function(n,t){if(t>0&&n.show&&null==n._paths){var r=function(n){for(var t=M(Qt-1,0,Ut-1),e=M($t+1,0,Ut-1);null==n[t]&&t>0;)t--;for(;null==n[e]&&Ut-1>e;)e++;return[t,e]}(e[t]);n._paths=n.paths(o,t,r[0],r[1])}})),W.forEach((function(n,t){t>0&&n.show&&(n._paths&&function(n){var t=W[n];if(1==le){var e=t._paths,r=e.stroke,i=e.fill,o=e.clip,l=D(t.width*an,3),a=l%2/2;oe(t.stroke,l,t.dash,t.fill),f.globalAlpha=t.alpha,f.translate(a,a),f.save();var u=ut,s=mt,c=dt,v=pt,h=l*an/2;0==t.min&&(v+=h),0==t.max&&(s-=h,v+=h),f.beginPath(),f.rect(u,s,c,v),f.clip(),null!=o&&f.clip(o),t.band?(f.fill(r),l&&f.stroke(r)):(l&&f.stroke(r),null!=t.fill&&f.fill(i)),f.restore(),f.translate(-a,-a),f.globalAlpha=1}t.band&&(le*=-1)}(t),n.points.show(o,t,Qt,$t)&&function(n){var t=W[n],r=t.points,i=D(r.width*an,3),o=i%2/2,l=r.width>0,a=(r.size-r.width)/2*an,u=D(2*a,3);f.translate(o,o),f.save(),f.beginPath(),f.rect(ut-u,mt-u,dt+2*u,pt+2*u),f.clip(),f.globalAlpha=t.alpha;for(var s=new Path2D,c=Qt;$t>=c;c++)if(null!=e[n][c]){var v=h(Ct(e[0][c],mn[gn],dt,ut)),m=h(Yt(e[n][c],mn[t.scale],pt,mt));s.moveTo(v+a,m),s.arc(v,m,a,0,2*_)}oe(r.stroke,i,null,r.fill||(l?"#fff":t.stroke)),f.fill(s),l&&f.stroke(s),f.globalAlpha=1,f.restore(),f.translate(-o,-o)}(t),rr("drawSeries",t))}))}(),rr("draw")),a||(a=!0,o.status=1,rr("ready")),ke=!1}function ze(t,r){var i=mn[t];if(null==i.from){if(0==Ut){var l=i.range(o,r.min,r.max,t);r.min=l[0],r.max=l[1]}if(Ut>1&&null!=r.min&&null!=r.max&&1e-16>r.max-r.min)return;t==gn&&2==i.distr&&Ut>0&&(r.min=n(r.min,e[0]),r.max=n(r.max,e[0])),bn[t]=r,gt=!0,Se()}}o.redraw=function(n){!1!==n?Ve(gn,mn[gn].min,mn[gn].max):Se()},o.setScale=ze;var De=!1,Ee=Rt.drag,We=Ee.x,Ye=Ee.y;Rt.show&&(Rt.x&&(ye=Rt.left,me=vn("u-cursor-x",z)),Rt.y&&(Me=Rt.top,de=vn("u-cursor-y",z)));var Ce,Ae,Fe,He=o.select=N({show:!0,over:!0,left:0,width:0,top:0,height:0},t.select),Pe=He.show?vn("u-select",He.over?z:x):null;function Ne(n,t){if(He.show){for(var e in n)fn(Pe,e,He[e]=n[e]);!1!==t&&rr("setSelect")}}function Ie(n){var t=jn?Bn[n][0].parentNode:null;W[n].show?t&&sn(t,tn):(t&&un(t,tn),Xt.length>1&&hn(Xt[n],-10,-10,rt,ot))}function Ve(n,t,e){ze(n,{min:t,max:e})}function Le(n,t,e){var r=W[n];if(null!=t.focus&&function(n){n!=Fe&&(W.forEach((function(t,e){!function(n,t){var e=W[n];Oe(n,t),e.band&&Oe(W[n+1].band?n+1:n-1,t)}(e,null==n||0==e||e==n?1:qt.alpha)})),Fe=n,Se())}(n),null!=t.show){if(r.show=t.show,Ie(n),r.band){var i=W[n+1]&&W[n+1].band?n+1:n-1;W[i].show=r.show,Ie(i)}Ve(r.scale,null,null),Se()}rr("setSeries",n,t),e&&lr.pub("setSeries",o,n,t)}function Oe(n,t){W[n].alpha=t,Rt.show&&Xt[n]&&(Xt[n].style.opacity=t),jn&&Bn[n]&&(Bn[n][0].parentNode.style.opacity=t)}function je(n,t){var e=rt;t!=gn&&(n=(e=ot)-n);var r=n/e,i=mn[t],o=i.min,l=i.max;return 3==i.distr?(o=w(o),l=w(l),g(10,o+(l-o)*r)):o+(l-o)*r}o.setSelect=Ne,o.setSeries=Le,jn&&Zt&&dn(K,Tn,(function(){Rt._lock||(Le(null,{focus:!1},ir.setSeries),Ge())})),o.valToIdx=function(t){return n(t,e[0])},o.posToIdx=function(t){return n(je(t,gn),e[0],Qt,$t)},o.posToVal=je,o.valToPos=function(n,t,e){return t==gn?Ct(n,mn[t],e?dt:rt,e?ut:0):Yt(n,mn[t],e?pt:ot,e?mt:0)},o.batch=function(n){n(o),Se()},o.setCursor=function(n){ye=n.left,Me=n.top,Ge()};var Be=0;function Ge(t,r){var i,l;if(Be=0,_e=ye,be=Me,i=Rt.move(o,ye,Me),ye=i[0],Me=i[1],Rt.show&&(Rt.x&&hn(me,h(ye),0,rt,ot),Rt.y&&hn(de,0,h(Me),rt,ot)),Ce=b,0>ye||0==Ut||Qt>$t){l=null;for(var u=0;W.length>u;u++)if(u>0&&Xt.length>1&&hn(Xt[u],-10,-10,rt,ot),jn&&On.live){if(0==u&&Gn)continue;for(var s=0;Bn[u].length>s;s++)Bn[u][s].firstChild.nodeValue="--"}Zt&&Le(null,{focus:!0},ir.setSeries)}else{var f=je(ye,gn);l=n(f,e[0],Qt,$t);for(var v=mn[gn],m=D(Ct(e[0][l],v,rt,0),3),p=0;W.length>p;p++){var g=W[p],w=Rt.dataIdx(o,p,l,f),x=w==l?m:D(Ct(e[0][w],v,rt,0),3);if(p>0&&g.show){var _=e[p][w],y=null==_?-10:D(Yt(_,mn[g.scale],ot,0),3);if(y>0){var M=c(y-Me);M>Ce||(Ce=M,Ae=p)}Xt.length>1&&hn(Xt[p],x,y,rt,ot)}if(jn&&On.live){if(w==Rt.idx&&!jt||0==p&&Gn)continue;var k=0==p&&2==_n?te:e[p],S=Gn?g.values(o,p,w):{_:g.value(o,k[w],p,w)},T=0;for(var z in S)Bn[p][T++].firstChild.nodeValue=S[z]}}jt=!1}if(He.show&&De)if(null!=r){var E=ir.scales,Y=E[0],C=E[1],A=r.cursor.drag;if(We=A._x,Ye=A._y,Y){var F=mn[Y],H=r.posToVal(r.select.left,Y),P=r.posToVal(r.select.left+r.select.width,Y);He.left=Ct(H,F,rt,0),He.width=c(He.left-Ct(P,F,rt,0)),fn(Pe,B,He.left),fn(Pe,V,He.width),C||(fn(Pe,O,He.top=0),fn(Pe,L,He.height=ot))}if(C){var N=mn[C],I=r.posToVal(r.select.top,C),j=r.posToVal(r.select.top+r.select.height,C);He.top=Yt(I,N,ot,0),He.height=c(He.top-Yt(j,N,ot,0)),fn(Pe,O,He.top),fn(Pe,L,He.height),Y||(fn(Pe,B,He.left=0),fn(Pe,V,He.width=rt))}}else{var G=c(_e-pe),R=c(be-ge);We=Ee.x&&G>=Ee.dist,Ye=Ee.y&&R>=Ee.dist;var U=Ee.uni;if(null!=U?We&&Ye&&(Ye=R>=U,(We=G>=U)||Ye||(R>G?Ye=!0:We=!0)):Ee.x&&Ee.y&&(We||Ye)&&(We=Ye=!0),We){var q=d(we,ye),Z=c(ye-we);fn(Pe,B,He.left=q),fn(Pe,V,He.width=Z),Ye||(fn(Pe,O,He.top=0),fn(Pe,L,He.height=ot))}if(Ye){var X=d(xe,Me),K=c(Me-xe);fn(Pe,O,He.top=X),fn(Pe,L,He.height=K),We||(fn(Pe,B,He.left=0),fn(Pe,V,He.width=rt))}We||Ye||(fn(Pe,L,He.height=0),fn(Pe,V,He.width=0))}Rt.idx=l,Rt.left=ye,Rt.top=Me,Ee._x=We,Ee._y=Ye,null!=t&&(lr.pub(J,o,ye,Me,rt,ot,l),Zt&&Le(Ce>qt.prox?null:Ae,{focus:!0},ir.setSeries)),a&&rr("setCursor")}var Re=null;function Ue(){Re=z.getBoundingClientRect()}function Je(n,t,e,r,i,o){Rt._lock||(qe(n,t,e,r,i,o,0,!1,null!=n),null!=n?0==Be&&(Be=rn(Ge)):Ge(null,t))}function qe(n,t,e,r,i,l,a,u,s){var f;if(null!=n)e=n.clientX-Re.left,r=n.clientY-Re.top;else{if(0>e||0>r)return ye=-10,void(Me=-10);var c=ir.scales,v=c[0],h=c[1];e=null!=v?Ct(t.posToVal(e,v),mn[v],rt,0):rt*(e/i),r=null!=h?Yt(t.posToVal(r,h),mn[h],ot,0):ot*(r/l)}s&&(e>1&&rt-1>e||(e=y(e,rt)),r>1&&ot-1>r||(r=y(r,ot))),u?(pe=e,ge=r,f=Rt.move(o,e,r),we=f[0],xe=f[1]):(ye=e,Me=r)}function Ze(){Ne({width:0,height:0},!1)}function Xe(n,t,e,r,i,l){De=!0,We=Ye=Ee._x=Ee._y=!1,qe(n,t,e,r,i,l,0,!0,!1),null!=n&&(Zn(Z,on,Ke),lr.pub(q,o,we,xe,rt,ot,null))}function Ke(n,t,e,r,i,l){De=Ee._x=Ee._y=!1,qe(n,t,e,r,i,l,0,!1,!0);var a=He.width>0||He.height>0;if(a&&Ne(He),Ee.setScale&&a){if(We&&Ve(gn,je(He.left,gn),je(He.left+He.width,gn)),Ye)for(var u in mn){var s=mn[u];u!=gn&&null==s.from&&s.min!=b&&Ve(u,je(He.top+He.height,u),je(He.top,u))}Ze()}else Rt.lock&&(Rt._lock=!Rt._lock,Rt._lock||Ge());null!=n&&(function(n,t){var e=qn.get(t)||{};pn(n,t,e[n]),e[n]=null}(Z,on),lr.pub(Z,o,ye,Me,rt,ot,null))}function Qe(){if(!Rt._lock){var n=De;if(De){var t=!0,e=!0;if(We&&Ye&&(t=10>=ye||ye>=rt-10,e=10>=Me||Me>=ot-10),We&&t){var r=ye,i=rt-ye,o=d(r,i);o==r&&(ye=0),o==i&&(ye=rt)}if(Ye&&e){var l=Me,a=ot-Me,u=d(l,a);u==l&&(Me=0),u==a&&(Me=ot)}Ge(1),De=!1}ye=-10,Me=-10,Ge(1),n&&(De=n)}}function $e(n){ie(),Ze(),null!=n&&lr.pub(Q,o,ye,Me,rt,ot,null)}var nr,tr={};tr.mousedown=Xe,tr.mousemove=Je,tr.mouseup=Ke,tr.dblclick=$e,tr.setSeries=function(n,t,e,r){Le(e,r)},Rt.show&&(Zn(q,z,Xe),Zn(J,z,Je),Zn(X,z,Ue),Zn(K,z,(function(){rn(Qe)})),Zn(Q,z,$e),nr=function(n){var t=null;function e(){t=null,n()}return function(){clearTimeout(t),t=setTimeout(e,100)}}(Ue),dn($,ln,nr),dn(nn,ln,nr),o.syncRect=Ue);var er=o.hooks=t.hooks||{};function rr(n,t,e){n in er&&er[n].forEach((function(n){n.call(null,o,t,e)}))}(t.plugins||[]).forEach((function(n){for(var t in n.hooks)er[t]=(er[t]||[]).concat(n.hooks[t])}));var ir=N({key:null,setSeries:!1,scales:[gn,null]},Rt.sync),or=ir.key,lr=null!=or?Tt[or]=Tt[or]||zt():zt();function ar(){rr("init",t,e),re(e||t.data,!1),bn[gn]?ze(gn,bn[gn]):ie(),Bt(t.width,t.height),Ne(He,!1)}return lr.sub(o),o.pub=function(n,t,e,r,i,o,l){tr[n](null,t,e,r,i,o,l)},o.destroy=function(){lr.unsub(o),pn($,ln,nr),pn(nn,ln,nr),u.remove(),rr("destroy")},i?i instanceof HTMLElement?(i.appendChild(u),ar()):i(o,ar):ar(),o}return Lt.assign=N,Lt.fmtNum=s,Lt.rangeNum=l,Lt.rangeLog=r,Lt.fmtDate=Sn,Lt.tzDate=function(n,t){var e;return"Etc/UTC"==t?e=new Date(+n+6e4*n.getTimezoneOffset()):t==Tn?e=n:(e=new Date(n.toLocaleString("en-US",{timeZone:t}))).setMilliseconds(n.getMilliseconds()),e},Lt}();
