terminaltables-3.1.10.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
terminaltables-3.1.10.dist-info/LICENSE,sha256=LhMg_TN9sGoxNtD0EBvWLxMknJXFtYEwTzVIHpedQyo,1065
terminaltables-3.1.10.dist-info/METADATA,sha256=BGcINTfvv7cpjjsSjwgWP8Shhu2ImBwGnAXS6WYAUEk,3509
terminaltables-3.1.10.dist-info/RECORD,,
terminaltables-3.1.10.dist-info/WHEEL,sha256=Fs47bPI6GdEUUVU2cC1ZH87S4TSequgq1XeoqimcqIE,87
terminaltables-3.1.10.dist-info/entry_points.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
terminaltables/__init__.py,sha256=fKc_1ny2v5lfHcQYtiFzojitbMW0N3teq3aoCbrZXj8,630
terminaltables/__pycache__/__init__.cpython-310.pyc,,
terminaltables/__pycache__/ascii_table.cpython-310.pyc,,
terminaltables/__pycache__/base_table.cpython-310.pyc,,
terminaltables/__pycache__/build.cpython-310.pyc,,
terminaltables/__pycache__/github_table.cpython-310.pyc,,
terminaltables/__pycache__/other_tables.cpython-310.pyc,,
terminaltables/__pycache__/terminal_io.cpython-310.pyc,,
terminaltables/__pycache__/width_and_alignment.cpython-310.pyc,,
terminaltables/ascii_table.py,sha256=39YHOMKRQrS3pIBt6VlunqgLmd_9VuoRnNHKHpIpsPk,2734
terminaltables/base_table.py,sha256=byS3S4_Q_dkC00F_PmBQOGH10Uk1sGNBfRG8aTvg07g,9738
terminaltables/build.py,sha256=bd8TQIwLCvaFiGUDhvMUWc-al1Wir6SZLa71UrWXAPw,5023
terminaltables/github_table.py,sha256=WaKnf9wQRNAKvueFQ3den7B89z1nZuSA-JTQbY1QlJg,2777
terminaltables/other_tables.py,sha256=0wFOru5TKt3rTy5zBH2HXR3Iz-m2amlJ2GgOSI_vmKk,8480
terminaltables/terminal_io.py,sha256=yLJ2yVtcgQ0s7eiQjc1Hd3Vmd6S13wzqYE5wcMpJfzs,3276
terminaltables/width_and_alignment.py,sha256=CtRlLJSYhyUiqdOL4rTyBpBEvnQs3oSV1jc-5qPwCAU,6176
