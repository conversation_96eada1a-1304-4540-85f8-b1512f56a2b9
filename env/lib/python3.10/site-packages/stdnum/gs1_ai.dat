# generated from https://www.gs1.org/standards/barcodes/application-identifiers
# on 2023-08-20 10:21:01.217615
00 format="N18" type="str" name="SSCC" description="Serial Shipping Container Code (SSCC)"
01 format="N14" type="str" name="GTIN" description="Global Trade Item Number (GTIN)"
02 format="N14" type="str" name="CONTENT" description="Global Trade Item Number (GTIN) of contained trade items"
10 format="X..20" type="str" fnc1="1" name="BATCH/LOT" description="Batch or lot number"
11 format="N6" type="date" name="PROD DATE" description="Production date (YYMMDD)"
12 format="N6" type="date" name="DUE DATE" description="Due date (YYMMDD)"
13 format="N6" type="date" name="PACK DATE" description="Packaging date (YYMMDD)"
15 format="N6" type="date" name="BEST BEFORE or BEST BY" description="Best before date (YYMMDD)"
16 format="N6" type="date" name="SELL BY" description="Sell by date (YYMMDD)"
17 format="N6" type="date" name="USE BY OR EXPIRY" description="Expiration date (YYMMDD)"
20 format="N2" type="str" name="VARIANT" description="Internal product variant"
21 format="X..20" type="str" fnc1="1" name="SERIAL" description="Serial number"
22 format="X..20" type="str" fnc1="1" name="CPV" description="Consumer product variant"
235 format="X..28" type="str" fnc1="1" name="TPX" description="Third Party Controlled, Serialised Extension of Global Trade Item Number (GTIN) (TPX)"
240 format="X..30" type="str" fnc1="1" name="ADDITIONAL ID" description="Additional product identification assigned by the manufacturer"
241 format="X..30" type="str" fnc1="1" name="CUST. PART No." description="Customer part number"
242 format="N..6" type="str" fnc1="1" name="MTO VARIANT" description="Made-to-Order variation number"
243 format="X..20" type="str" fnc1="1" name="PCN" description="Packaging component number"
250 format="X..30" type="str" fnc1="1" name="SECONDARY SERIAL" description="Secondary serial number"
251 format="X..30" type="str" fnc1="1" name="REF. TO SOURCE" description="Reference to source entity"
253 format="N13[+X..17]" type="str" fnc1="1" name="GDTI" description="Global Document Type Identifier (GDTI)"
254 format="X..20" type="str" fnc1="1" name="GLN EXTENSION COMPONENT" description="Global Location Number (GLN) extension component"
255 format="N13[+N..12]" type="str" fnc1="1" name="GCN" description="Global Coupon Number (GCN)"
30 format="N..8" type="int" fnc1="1" name="VAR. COUNT" description="Variable count of items (variable measure trade item)"
310 format="N6" type="decimal" name="NET WEIGHT (kg)" description="Net weight, kilograms (variable measure trade item)"
311 format="N6" type="decimal" name="LENGTH (m)" description="Length or first dimension, metres (variable measure trade item)"
312 format="N6" type="decimal" name="WIDTH (m)" description="Width, diameter, or second dimension, metres (variable measure trade item)"
313 format="N6" type="decimal" name="HEIGHT (m)" description="Depth, thickness, height, or third dimension, metres (variable measure trade item)"
314 format="N6" type="decimal" name="AREA (m<sup>2</sup>)" description="Area, square metres (variable measure trade item)"
315 format="N6" type="decimal" name="NET VOLUME (l)" description="Net volume, litres (variable measure trade item)"
316 format="N6" type="decimal" name="NET VOLUME (m<sup>3</sup>)" description="Net volume, cubic metres (variable measure trade item)"
320 format="N6" type="decimal" name="NET WEIGHT (lb)" description="Net weight, pounds (variable measure trade item)"
321 format="N6" type="decimal" name="LENGTH (in)" description="Length or first dimension, inches (variable measure trade item)"
322 format="N6" type="decimal" name="LENGTH (ft)" description="Length or first dimension, feet (variable measure trade item)"
323 format="N6" type="decimal" name="LENGTH (yd)" description="Length or first dimension, yards (variable measure trade item)"
324 format="N6" type="decimal" name="WIDTH (in)" description="Width, diameter, or second dimension, inches (variable measure trade item)"
325 format="N6" type="decimal" name="WIDTH (ft)" description="Width, diameter, or second dimension, feet (variable measure trade item)"
326 format="N6" type="decimal" name="WIDTH (yd)" description="Width, diameter, or second dimension, yards (variable measure trade item)"
327 format="N6" type="decimal" name="HEIGHT (in)" description="Depth, thickness, height, or third dimension, inches (variable measure trade item)"
328 format="N6" type="decimal" name="HEIGHT (ft)" description="Depth, thickness, height, or third dimension, feet (variable measure trade item)"
329 format="N6" type="decimal" name="HEIGHT (yd)" description="Depth, thickness, height, or third dimension, yards (variable measure trade item)"
330 format="N6" type="decimal" name="GROSS WEIGHT (kg)" description="Logistic weight, kilograms"
331 format="N6" type="decimal" name="LENGTH (m), log" description="Length or first dimension, metres"
332 format="N6" type="decimal" name="WIDTH (m), log" description="Width, diameter, or second dimension, metres"
333 format="N6" type="decimal" name="HEIGHT (m), log" description="Depth, thickness, height, or third dimension, metres"
334 format="N6" type="decimal" name="AREA (m<sup>2</sup>), log" description="Area, square metres"
335 format="N6" type="decimal" name="VOLUME (l), log" description="Logistic volume, litres"
336 format="N6" type="decimal" name="VOLUME (m<sup>3</sup>), log" description="Logistic volume, cubic metres"
337 format="N6" type="decimal" name="KG PER m<sup>2</sup>" description="Kilograms per square metre"
340 format="N6" type="decimal" name="GROSS WEIGHT (lb)" description="Logistic weight, pounds"
341 format="N6" type="decimal" name="LENGTH (in), log" description="Length or first dimension, inches"
342 format="N6" type="decimal" name="LENGTH (ft), log" description="Length or first dimension, feet"
343 format="N6" type="decimal" name="LENGTH (yd), log" description="Length or first dimension, yards"
344 format="N6" type="decimal" name="WIDTH (in), log" description="Width, diameter, or second dimension, inches"
345 format="N6" type="decimal" name="WIDTH (ft), log" description="Width, diameter, or second dimension, feet"
346 format="N6" type="decimal" name="WIDTH (yd), log" description="Width, diameter, or second dimension, yard"
347 format="N6" type="decimal" name="HEIGHT (in), log" description="Depth, thickness, height, or third dimension, inches"
348 format="N6" type="decimal" name="HEIGHT (ft), log" description="Depth, thickness, height, or third dimension, feet"
349 format="N6" type="decimal" name="HEIGHT (yd), log" description="Depth, thickness, height, or third dimension, yards"
350 format="N6" type="decimal" name="AREA (in<sup>2</sup>)" description="Area, square inches (variable measure trade item)"
351 format="N6" type="decimal" name="AREA (ft<sup>2</sup>)" description="Area, square feet (variable measure trade item)"
352 format="N6" type="decimal" name="AREA (yd<sup>2</sup>)" description="Area, square yards (variable measure trade item)"
353 format="N6" type="decimal" name="AREA (in<sup>2</sup>), log" description="Area, square inches"
354 format="N6" type="decimal" name="AREA (ft<sup>2</sup>), log" description="Area, square feet"
355 format="N6" type="decimal" name="AREA (yd<sup>2</sup>), log" description="Area, square yards"
356 format="N6" type="decimal" name="NET WEIGHT (t oz)" description="Net weight, troy ounces (variable measure trade item)"
357 format="N6" type="decimal" name="NET VOLUME (oz)" description="Net weight (or volume), ounces (variable measure trade item)"
360 format="N6" type="decimal" name="NET VOLUME (qt)" description="Net volume, quarts (variable measure trade item)"
361 format="N6" type="decimal" name="NET VOLUME (gal.)" description="Net volume, gallons U.S. (variable measure trade item)"
362 format="N6" type="decimal" name="VOLUME (qt), log" description="Logistic volume, quarts"
363 format="N6" type="decimal" name="VOLUME (gal.), log" description="Logistic volume, gallons U.S."
364 format="N6" type="decimal" name="VOLUME (in<sup>3</sup>)" description="Net volume, cubic inches (variable measure trade item)"
365 format="N6" type="decimal" name="VOLUME (ft<sup>3</sup>)" description="Net volume, cubic feet (variable measure trade item)"
366 format="N6" type="decimal" name="VOLUME (yd<sup>3</sup>)" description="Net volume, cubic yards (variable measure trade item)"
367 format="N6" type="decimal" name="VOLUME (in<sup>3</sup>), log" description="Logistic volume, cubic inches"
368 format="N6" type="decimal" name="VOLUME (ft<sup>3</sup>), log" description="Logistic volume, cubic feet"
369 format="N6" type="decimal" name="VOLUME (yd<sup>3</sup>), log" description="Logistic volume, cubic yards"
37 format="N..8" type="int" fnc1="1" name="COUNT" description="Count of trade items or trade item pieces contained in a logistic unit"
390 format="N..15" type="decimal" fnc1="1" name="AMOUNT" description="Applicable amount payable or Coupon value, local currency"
391 format="N3+N..15" type="decimal" fnc1="1" name="AMOUNT" description="Applicable amount payable with ISO currency code"
392 format="N..15" type="decimal" fnc1="1" name="PRICE" description="Applicable amount payable, single monetary area (variable measure trade item)"
393 format="N3+N..15" type="decimal" fnc1="1" name="PRICE" description="Applicable amount payable with ISO currency code (variable measure trade item)"
394 format="N4" type="decimal" fnc1="1" name="PRCNT OFF" description="Percentage discount of a coupon"
395 format="N6" type="decimal" fnc1="1" name="PRICE/UoM" description="Amount Payable per unit of measure single monetary area (variable measure trade item)"
400 format="X..30" type="str" fnc1="1" name="ORDER NUMBER" description="Customers purchase order number"
401 format="X..30" type="str" fnc1="1" name="GINC" description="Global Identification Number for Consignment (GINC)"
402 format="N17" type="str" fnc1="1" name="GSIN" description="Global Shipment Identification Number (GSIN)"
403 format="X..30" type="str" fnc1="1" name="ROUTE" description="Routing code"
410 format="N13" type="str" name="SHIP TO LOC" description="Ship to / Deliver to Global Location Number (GLN)"
411 format="N13" type="str" name="BILL TO" description="Bill to / Invoice to Global Location Number (GLN)"
412 format="N13" type="str" name="PURCHASE FROM" description="Purchased from Global Location Number (GLN)"
413 format="N13" type="str" name="SHIP FOR LOC" description="Ship for / Deliver for - Forward to Global Location Number (GLN)"
414 format="N13" type="str" name="LOC No." description="Identification of a physical location - Global Location Number (GLN)"
415 format="N13" type="str" name="PAY TO" description="Global Location Number (GLN) of the invoicing party"
416 format="N13" type="str" name="PROD/SERV LOC" description="Global Location Number (GLN) of the production or service location"
417 format="N13" type="str" name="PARTY" description="Party Global Location Number (GLN)"
420 format="X..20" type="str" fnc1="1" name="SHIP TO POST" description="Ship to / Deliver to postal code within a single postal authority"
421 format="N3+X..9" type="str" fnc1="1" name="SHIP TO POST" description="Ship to / Deliver to postal code with ISO country code"
422 format="N3" type="int" fnc1="1" name="ORIGIN" description="Country of origin of a trade item"
423 format="N3+N..12" type="str" fnc1="1" name="COUNTRY - INITIAL PROCESS" description="Country of initial processing"
424 format="N3" type="int" fnc1="1" name="COUNTRY - PROCESS" description="Country of processing"
425 format="N3+N..12" type="str" fnc1="1" name="COUNTRY - DISASSEMBLY" description="Country of disassembly"
426 format="N3" type="int" fnc1="1" name="COUNTRY - FULL PROCESS" description="Country covering full process chain"
427 format="X..3" type="str" fnc1="1" name="ORIGIN SUBDIVISION" description="Country subdivision Of origin"
4300 format="X..35" type="str" fnc1="1" name="SHIP TO COMP" description="Ship-to / Deliver-to company name"
4301 format="X..35" type="str" fnc1="1" name="SHIP TO NAME" description="Ship-to / Deliver-to contact"
4302 format="X..70" type="str" fnc1="1" name="SHIP TO ADD1" description="Ship-to / Deliver-to address line 1"
4303 format="X..70" type="str" fnc1="1" name="SHIP TO ADD2" description="Ship-to / Deliver-to address line 2"
4304 format="X..70" type="str" fnc1="1" name="SHIP TO SUB" description="Ship-to / Deliver-to suburb"
4305 format="X..70" type="str" fnc1="1" name="SHIP TO LOC" description="Ship-to / Deliver-to locality"
4306 format="X..70" type="str" fnc1="1" name="SHIP TO REG" description="Ship-to / Deliver-to region"
4307 format="X2" type="str" fnc1="1" name="SHIP TO COUNTRY" description="Ship-to / Deliver-to country code"
4308 format="X..30" type="str" fnc1="1" name="SHIP TO PHONE" description="Ship-to / Deliver-to telephone number"
4309 format="N20" type="str" fnc1="1" name="SHIP TO GEO" description="Ship-to / Deliver-to GEO location"
4310 format="X..35" type="str" fnc1="1" name="RTN TO COMP" description="Return-to company name"
4311 format="X..35" type="str" fnc1="1" name="RTN TO NAME" description="Return-to contact"
4312 format="X..70" type="str" fnc1="1" name="RTN TO ADD1" description="Return-to address line 1"
4313 format="X..70" type="str" fnc1="1" name="RTN TO ADD2" description="Return-to address line 2"
4314 format="X..70" type="str" fnc1="1" name="RTN TO SUB" description="Return-to suburb"
4315 format="X..70" type="str" fnc1="1" name="RTN TO LOC" description="Return-to locality"
4316 format="X..70" type="str" fnc1="1" name="RTN TO REG" description="Return-to region"
4317 format="X2" type="str" fnc1="1" name="RTN TO COUNTRY" description="Return-to country code"
4318 format="X..20" type="str" fnc1="1" name="RTN TO POST" description="Return-to postal code"
4319 format="X..30" type="str" fnc1="1" name="RTN TO PHONE" description="Return-to telephone number"
4320 format="X..35" type="str" fnc1="1" name="SRV DESCRIPTION" description="Service code description"
4321 format="N1" type="str" fnc1="1" name="DANGEROUS GOODS" description="Dangerous goods flag"
4322 format="N1" type="str" fnc1="1" name="AUTH TO LEAVE" description="Authority to leave"
4323 format="N1" type="str" fnc1="1" name="SIG REQUIRED" description="Signature required flag"
4324 format="N10" type="date" fnc1="1" name="NBEF DEL DT" description="Not before delivery date time"
4325 format="N10" type="date" fnc1="1" name="NAFT DEL DT" description="Not after delivery date time"
4326 format="N6" type="date" fnc1="1" name="REL DATE" description="Release date"
7001 format="N13" type="str" fnc1="1" name="NSN" description="NATO Stock Number (NSN)"
7002 format="X..30" type="str" fnc1="1" name="MEAT CUT" description="UN/ECE meat carcasses and cuts classification"
7003 format="N10" type="date" fnc1="1" name="EXPIRY TIME" description="Expiration date and time"
7004 format="N..4" type="str" fnc1="1" name="ACTIVE POTENCY" description="Active potency"
7005 format="X..12" type="str" fnc1="1" name="CATCH AREA" description="Catch area"
7006 format="N6" type="date" fnc1="1" name="FIRST FREEZE DATE" description="First freeze date"
7007 format="N6..12" type="date" fnc1="1" name="HARVEST DATE" description="Harvest date"
7008 format="X..3" type="str" fnc1="1" name="AQUATIC SPECIES" description="Species for fishery purposes"
7009 format="X..10" type="str" fnc1="1" name="FISHING GEAR TYPE" description="Fishing gear type"
7010 format="X..2" type="str" fnc1="1" name="PROD METHOD" description="Production method"
7011 format="N6[+N..4]" type="date" fnc1="1" name="TEST BY DATE" description="Test by date"
7020 format="X..20" type="str" fnc1="1" name="REFURB LOT" description="Refurbishment lot ID"
7021 format="X..20" type="str" fnc1="1" name="FUNC STAT" description="Functional status"
7022 format="X..20" type="str" fnc1="1" name="REV STAT" description="Revision status"
7023 format="X..30" type="str" fnc1="1" name="GIAI - ASSEMBLY" description="Global Individual Asset Identifier (GIAI) of an assembly"
7030 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 0" description="Number of processor with ISO Country Code"
7031 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 1" description="Number of processor with ISO Country Code"
7032 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 2" description="Number of processor with ISO Country Code"
7033 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 3" description="Number of processor with ISO Country Code"
7034 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 4" description="Number of processor with ISO Country Code"
7035 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 5" description="Number of processor with ISO Country Code"
7036 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 6" description="Number of processor with ISO Country Code"
7037 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 7" description="Number of processor with ISO Country Code"
7038 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 8" description="Number of processor with ISO Country Code"
7039 format="N3+X..27" type="str" fnc1="1" name="PROCESSOR # 9" description="Number of processor with ISO Country Code"
7040 format="N1+X3" type="str" fnc1="1" name="UIC+EXT" description="GS1 UIC with Extension 1 and Importer index"
710 format="X..20" type="str" fnc1="1" name="NHRN PZN" description="National Healthcare Reimbursement Number (NHRN) - Germany PZN"
711 format="X..20" type="str" fnc1="1" name="NHRN CIP" description="National Healthcare Reimbursement Number (NHRN) - France CIP"
712 format="X..20" type="str" fnc1="1" name="NHRN CN" description="National Healthcare Reimbursement Number (NHRN) - Spain CN"
713 format="X..20" type="str" fnc1="1" name="NHRN DRN" description="National Healthcare Reimbursement Number (NHRN) - Brasil DRN"
714 format="X..20" type="str" fnc1="1" name="NHRN AIM" description="National Healthcare Reimbursement Number (NHRN) - Portugal AIM"
715 format="X..20" type="str" fnc1="1" name="NHRN NDC" description="National Healthcare Reimbursement Number (NHRN) - United States of America NDC"
7230 format="X2+X..28" type="str" fnc1="1" name="CERT #1" description="Certification reference"
7231 format="X2+X..28" type="str" fnc1="1" name="CERT #2" description="Certification reference"
7232 format="X2+X..28" type="str" fnc1="1" name="CERT #3" description="Certification reference"
7233 format="X2+X..28" type="str" fnc1="1" name="CERT #4" description="Certification reference"
7234 format="X2+X..28" type="str" fnc1="1" name="CERT #5" description="Certification reference"
7235 format="X2+X..28" type="str" fnc1="1" name="CERT #6" description="Certification reference"
7236 format="X2+X..28" type="str" fnc1="1" name="CERT #7" description="Certification reference"
7237 format="X2+X..28" type="str" fnc1="1" name="CERT #8" description="Certification reference"
7238 format="X2+X..28" type="str" fnc1="1" name="CERT #9" description="Certification reference"
7239 format="X2+X..28" type="str" fnc1="1" name="CERT #10" description="Certification reference"
7240 format="X..20" type="str" fnc1="1" name="PROTOCOL" description="Protocol ID"
8001 format="N14" type="str" fnc1="1" name="DIMENSIONS" description="Roll products (width, length, core diameter, direction, splices)"
8002 format="X..20" type="str" fnc1="1" name="CMT No." description="Cellular mobile telephone identifier"
8003 format="N14+X..16" type="str" fnc1="1" name="GRAI" description="Global Returnable Asset Identifier (GRAI)"
8004 format="X..30" type="str" fnc1="1" name="GIAI" description="Global Individual Asset Identifier (GIAI)"
8005 format="N6" type="str" fnc1="1" name="PRICE PER UNIT" description="Price per unit of measure"
8006 format="N14+N2+N2" type="str" fnc1="1" name="ITIP" description="Identification of an individual trade item piece (ITIP)"
8007 format="X..34" type="str" fnc1="1" name="IBAN" description="International Bank Account Number (IBAN)"
8008 format="N8[+N..4]" type="date" fnc1="1" name="PROD TIME" description="Date and time of production"
8009 format="X..50" type="str" fnc1="1" name="OPTSEN" description="Optically Readable Sensor Indicator"
8010 format="Y..30" type="str" fnc1="1" name="CPID" description="Component/Part Identifier (CPID)"
8011 format="N..12" type="str" fnc1="1" name="CPID SERIAL" description="Component/Part Identifier serial number (CPID SERIAL)"
8012 format="X..20" type="str" fnc1="1" name="VERSION" description="Software version"
8013 format="X..25" type="str" fnc1="1" name="GMN (for medical devices, the default, global data title is BUDI-DI)" description="Global Model Number (GMN)"
8017 format="N18" type="str" fnc1="1" name="GSRN - PROVIDER" description="Global Service Relation Number (GSRN) to identify the relationship between an organisation offering services and the provider of services"
8018 format="N18" type="str" fnc1="1" name="GSRN - RECIPIENT" description="Global Service Relation Number (GSRN) to identify the relationship between an organisation offering services and the recipient of services"
8019 format="N..10" type="str" fnc1="1" name="SRIN" description="Service Relation Instance Number (SRIN)"
8020 format="X..25" type="str" fnc1="1" name="REF No." description="Payment slip reference number"
8026 format="N14+N2+N2" type="str" fnc1="1" name="ITIP CONTENT" description="Identification of pieces of a trade item (ITIP) contained in a logistic unit"
8110 format="X..70" type="str" fnc1="1" name="" description="Coupon code identification for use in North America"
8111 format="N4" type="str" fnc1="1" name="POINTS" description="Loyalty points of a coupon"
8112 format="X..70" type="str" fnc1="1" name="" description="Paperless coupon code identification for use in North America"
8200 format="X..70" type="str" fnc1="1" name="PRODUCT URL" description="Extended Packaging URL"
90 format="X..30" type="str" fnc1="1" name="INTERNAL" description="Information mutually agreed between trading partners"
91-99 format="X..90" type="str" fnc1="1" name="INTERNAL" description="Company internal information"
