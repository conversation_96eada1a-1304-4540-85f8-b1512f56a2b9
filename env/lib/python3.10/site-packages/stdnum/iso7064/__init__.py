# __init__.py - functions for performing the ISO 7064 algorithms
#
# Copyright (C) 2010, 2012 <PERSON>
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
# 02110-1301 USA

"""Collection of the ISO 7064 algorithms.

This package provides a number of modules for calculation and verification
of numbers using one of the ISO 7064 algorithms.

Note that these functions were not implemented using the ISO text itself
because the text is not available for free. These functions were
implemented based on information on the algorithms found online and some
reverse engineering. If anyone can provide a legal copy of the ISO/IEC
7064 standard these functions can be validated and perhaps improved.
"""
