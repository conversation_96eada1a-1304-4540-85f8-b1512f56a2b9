stripe-2.56.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
stripe-2.56.0.dist-info/LICENSE,sha256=iyi8_6voinKMxI032Qe9df69Ducl_XdJRpEtyjG8YCc,1092
stripe-2.56.0.dist-info/METADATA,sha256=GUvVX8boZ8ASRehOquSy-Vh-a7dKR8VlwgQmmY1rqAM,2440
stripe-2.56.0.dist-info/RECORD,,
stripe-2.56.0.dist-info/WHEEL,sha256=Z-nyYpwrcSqxfdux5Mbn_DQ525iP7J2DG3JgGvOYyTQ,110
stripe-2.56.0.dist-info/top_level.txt,sha256=hYA8RowzYrvJYWbyp6CB9658bSJyzspnHeOvL7AifMk,7
stripe/__init__.py,sha256=rTrfXoI5GZqmEX91FMvvJsntdQhpJ5UR3nIF8reW94U,1399
stripe/__pycache__/__init__.cpython-310.pyc,,
stripe/__pycache__/api_requestor.cpython-310.pyc,,
stripe/__pycache__/error.cpython-310.pyc,,
stripe/__pycache__/http_client.cpython-310.pyc,,
stripe/__pycache__/multipart_data_generator.cpython-310.pyc,,
stripe/__pycache__/oauth.cpython-310.pyc,,
stripe/__pycache__/oauth_error.cpython-310.pyc,,
stripe/__pycache__/object_classes.cpython-310.pyc,,
stripe/__pycache__/request_metrics.cpython-310.pyc,,
stripe/__pycache__/six.cpython-310.pyc,,
stripe/__pycache__/stripe_object.cpython-310.pyc,,
stripe/__pycache__/stripe_response.cpython-310.pyc,,
stripe/__pycache__/util.cpython-310.pyc,,
stripe/__pycache__/version.cpython-310.pyc,,
stripe/__pycache__/webhook.cpython-310.pyc,,
stripe/api_requestor.py,sha256=YOnA6rrlHLQe7mXXVZ5QEhYfbww_eUP6xxBp1Hn2Dr8,13139
stripe/api_resources/__init__.py,sha256=6H3SMn4U00Re0ldSyRlfhTYkHjNRs3SLkpHaLhz4Te4,4251
stripe/api_resources/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/__pycache__/account.cpython-310.pyc,,
stripe/api_resources/__pycache__/account_link.cpython-310.pyc,,
stripe/api_resources/__pycache__/alipay_account.cpython-310.pyc,,
stripe/api_resources/__pycache__/apple_pay_domain.cpython-310.pyc,,
stripe/api_resources/__pycache__/application_fee.cpython-310.pyc,,
stripe/api_resources/__pycache__/application_fee_refund.cpython-310.pyc,,
stripe/api_resources/__pycache__/balance.cpython-310.pyc,,
stripe/api_resources/__pycache__/balance_transaction.cpython-310.pyc,,
stripe/api_resources/__pycache__/bank_account.cpython-310.pyc,,
stripe/api_resources/__pycache__/bitcoin_receiver.cpython-310.pyc,,
stripe/api_resources/__pycache__/bitcoin_transaction.cpython-310.pyc,,
stripe/api_resources/__pycache__/capability.cpython-310.pyc,,
stripe/api_resources/__pycache__/card.cpython-310.pyc,,
stripe/api_resources/__pycache__/charge.cpython-310.pyc,,
stripe/api_resources/__pycache__/country_spec.cpython-310.pyc,,
stripe/api_resources/__pycache__/coupon.cpython-310.pyc,,
stripe/api_resources/__pycache__/credit_note.cpython-310.pyc,,
stripe/api_resources/__pycache__/credit_note_line_item.cpython-310.pyc,,
stripe/api_resources/__pycache__/customer.cpython-310.pyc,,
stripe/api_resources/__pycache__/customer_balance_transaction.cpython-310.pyc,,
stripe/api_resources/__pycache__/dispute.cpython-310.pyc,,
stripe/api_resources/__pycache__/ephemeral_key.cpython-310.pyc,,
stripe/api_resources/__pycache__/error_object.cpython-310.pyc,,
stripe/api_resources/__pycache__/event.cpython-310.pyc,,
stripe/api_resources/__pycache__/exchange_rate.cpython-310.pyc,,
stripe/api_resources/__pycache__/file.cpython-310.pyc,,
stripe/api_resources/__pycache__/file_link.cpython-310.pyc,,
stripe/api_resources/__pycache__/invoice.cpython-310.pyc,,
stripe/api_resources/__pycache__/invoice_item.cpython-310.pyc,,
stripe/api_resources/__pycache__/invoice_line_item.cpython-310.pyc,,
stripe/api_resources/__pycache__/issuer_fraud_record.cpython-310.pyc,,
stripe/api_resources/__pycache__/line_item.cpython-310.pyc,,
stripe/api_resources/__pycache__/list_object.cpython-310.pyc,,
stripe/api_resources/__pycache__/login_link.cpython-310.pyc,,
stripe/api_resources/__pycache__/mandate.cpython-310.pyc,,
stripe/api_resources/__pycache__/order.cpython-310.pyc,,
stripe/api_resources/__pycache__/order_return.cpython-310.pyc,,
stripe/api_resources/__pycache__/payment_intent.cpython-310.pyc,,
stripe/api_resources/__pycache__/payment_method.cpython-310.pyc,,
stripe/api_resources/__pycache__/payout.cpython-310.pyc,,
stripe/api_resources/__pycache__/person.cpython-310.pyc,,
stripe/api_resources/__pycache__/plan.cpython-310.pyc,,
stripe/api_resources/__pycache__/price.cpython-310.pyc,,
stripe/api_resources/__pycache__/product.cpython-310.pyc,,
stripe/api_resources/__pycache__/promotion_code.cpython-310.pyc,,
stripe/api_resources/__pycache__/recipient.cpython-310.pyc,,
stripe/api_resources/__pycache__/recipient_transfer.cpython-310.pyc,,
stripe/api_resources/__pycache__/refund.cpython-310.pyc,,
stripe/api_resources/__pycache__/reversal.cpython-310.pyc,,
stripe/api_resources/__pycache__/review.cpython-310.pyc,,
stripe/api_resources/__pycache__/setup_attempt.cpython-310.pyc,,
stripe/api_resources/__pycache__/setup_intent.cpython-310.pyc,,
stripe/api_resources/__pycache__/sku.cpython-310.pyc,,
stripe/api_resources/__pycache__/source.cpython-310.pyc,,
stripe/api_resources/__pycache__/source_transaction.cpython-310.pyc,,
stripe/api_resources/__pycache__/subscription.cpython-310.pyc,,
stripe/api_resources/__pycache__/subscription_item.cpython-310.pyc,,
stripe/api_resources/__pycache__/subscription_schedule.cpython-310.pyc,,
stripe/api_resources/__pycache__/tax_id.cpython-310.pyc,,
stripe/api_resources/__pycache__/tax_rate.cpython-310.pyc,,
stripe/api_resources/__pycache__/three_d_secure.cpython-310.pyc,,
stripe/api_resources/__pycache__/token.cpython-310.pyc,,
stripe/api_resources/__pycache__/topup.cpython-310.pyc,,
stripe/api_resources/__pycache__/transfer.cpython-310.pyc,,
stripe/api_resources/__pycache__/usage_record.cpython-310.pyc,,
stripe/api_resources/__pycache__/usage_record_summary.cpython-310.pyc,,
stripe/api_resources/__pycache__/webhook_endpoint.cpython-310.pyc,,
stripe/api_resources/abstract/__init__.py,sha256=ezAnC5A9FclJ3M0mJqfks6ZEZDr7T9k7URrBiAaUzak,877
stripe/api_resources/abstract/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/createable_api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/custom_method.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/deletable_api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/listable_api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/nested_resource_class_methods.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/singleton_api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/updateable_api_resource.cpython-310.pyc,,
stripe/api_resources/abstract/__pycache__/verify_mixin.cpython-310.pyc,,
stripe/api_resources/abstract/api_resource.py,sha256=DJSa3h-bwOe-4bWNNj5cZg6hhzpWlznWHpBXtbIG5Ew,2313
stripe/api_resources/abstract/createable_api_resource.py,sha256=bktJf74bs8DzYPT3wl3_NRYJNICeN0aO0sTY79F-4Pg,808
stripe/api_resources/abstract/custom_method.py,sha256=RvGmlz_GvpqGvtv0f36_B1yLRv7ax4kBGMSim-I5iIU,1611
stripe/api_resources/abstract/deletable_api_resource.py,sha256=HmVK50g1ZoR_gUvDV6sttyXpQVsnfIhjy3LsKwSSE7c,619
stripe/api_resources/abstract/listable_api_resource.py,sha256=w5i9RDoj6liitn4lphU71oT6us0514oht59sz6K5PiY,948
stripe/api_resources/abstract/nested_resource_class_methods.py,sha256=GgxGm46fg2a2lW_ymBpOJVEaGSD7aBWx4AEnptt6Ssw,4357
stripe/api_resources/abstract/singleton_api_resource.py,sha256=2y2Y5iTjhkWcLEWTr702hYOENtzu-D8R1AELD6TOTs8,874
stripe/api_resources/abstract/updateable_api_resource.py,sha256=ZPmrXeu3xZt3lDo9LB8iZ4taD89_aBTGWJO3iix97ew,887
stripe/api_resources/abstract/verify_mixin.py,sha256=EKpcQuTnZwNu-Z91jR0Oz8mAU3WsIg587p8Z785iqik,366
stripe/api_resources/account.py,sha256=of4I8t6pFj_KlGJqSd_-nYO77_5noQpMvQTzOdsDNS4,2957
stripe/api_resources/account_link.py,sha256=PZ2bWtY91dZp5-dMlkKN0kE92fg1eLwYmWdUiN72sBg,207
stripe/api_resources/alipay_account.py,sha256=Ey6Ns2Nd4-9uHWgoXPGofhwBNncMnFMBliuBUPMlmbE,1356
stripe/api_resources/apple_pay_domain.py,sha256=m1H9BF28SK_lgnWsfFFiQ51in9PT9I0T4npqgI0WvFQ,469
stripe/api_resources/application_fee.py,sha256=QVcnyhFO2-Mc9Il6kc7-9GePwYIgu4AK8lvfpmU8fzM,653
stripe/api_resources/application_fee_refund.py,sha256=qUXOgb6vF7fu1eKi9ii1wTpi4OuZxlqhunsoosolaDs,1138
stripe/api_resources/balance.py,sha256=CKU0AAHC44_owCMZqlh65v1Ihi9ySllrJTbkEd4iAoU,196
stripe/api_resources/balance_transaction.py,sha256=2slkvtWpv-Oa6Lm-ePcnpe0rF6VybG75F6WNCQJvPCI,217
stripe/api_resources/bank_account.py,sha256=XiPEC0RzlWRPcCXLR75G-ZBXtujotsb2QcNAIpCtvRY,2143
stripe/api_resources/billing_portal/__init__.py,sha256=LpVjVSTv9Drt5FotiuderI0gW8Ns64AIL9He_g493Ro,222
stripe/api_resources/billing_portal/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/billing_portal/__pycache__/configuration.cpython-310.pyc,,
stripe/api_resources/billing_portal/__pycache__/session.cpython-310.pyc,,
stripe/api_resources/billing_portal/configuration.py,sha256=61PuAWlRWA4hiFbqW4nosxiGmAck0FJclKXuctOU6xo,401
stripe/api_resources/billing_portal/session.py,sha256=mxr74tdYzyFhkTK3XVvDfPE0RLXyFTzbcnc_YtfK3Xk,213
stripe/api_resources/bitcoin_receiver.py,sha256=c987T0lQ9Gy5m3b6D7FmyoWSeeBBH0TEObEkj4AevqI,856
stripe/api_resources/bitcoin_transaction.py,sha256=vKr1abYIeSXAqNALbejzXBEXvmX5i0IEKbXnECRo78s,194
stripe/api_resources/capability.py,sha256=6O9oPSXXVLr0ShKdbzcHBEArO6LpipKoW0EFsIjkeQk,1144
stripe/api_resources/card.py,sha256=h_OAsW9IbO9qbF7rDgvj4JMo6n2dTfnuTTI_ClLDH1k,2428
stripe/api_resources/charge.py,sha256=qxlobeyJgmH9StXGdHfNbDEZeVOTvciqp6OIqRudxjY,2638
stripe/api_resources/checkout/__init__.py,sha256=a8QOAZakim43T-1lGOVImTNaT7sSi9in1J6IfT1UGas,140
stripe/api_resources/checkout/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/checkout/__pycache__/session.cpython-310.pyc,,
stripe/api_resources/checkout/session.py,sha256=ZtAle8OhZgqXBjL7kp2_93SeczqoFZWSNELF-9ow-60,427
stripe/api_resources/country_spec.py,sha256=v3kIsG7C95Wed2mGxt2sGjVdSeiYC_iP9W-Xu7Oo39g,203
stripe/api_resources/coupon.py,sha256=gAuyCI-WCoIcoKMGCEMXZZsdziC3aaULs2_SN1PIjVk,470
stripe/api_resources/credit_note.py,sha256=Qb5J0XGlh7OTXCOYdf4XN9XR_cZe7EA9ZksVuA1VSPY,1298
stripe/api_resources/credit_note_line_item.py,sha256=m-FT2QLgOtQ1Kqy5aRYWMOg0f7laVEQpOqRqKn7GN88,196
stripe/api_resources/customer.py,sha256=6hhx8_ic-IE7RnX_o19Ab6yeGrIW3p3KzJr55Qvt0sI,1417
stripe/api_resources/customer_balance_transaction.py,sha256=zlSZ8Wh-JExar_hmXPVnBkuxGrXKleQjZ8yhXF2uw5o,924
stripe/api_resources/dispute.py,sha256=sHYzIPBbseJDbpCaAP2B1qsDPRVeO6Ehat13urPaXfI,649
stripe/api_resources/ephemeral_key.py,sha256=ho_c2XfpGL982CbqEvYzOdK-m3rTnFDOJ2ZGHXd8YQ0,1034
stripe/api_resources/error_object.py,sha256=uBRQMv85cvPZIxQDQSxAgiHbyyFHrxeIbIe-8wVfZ8A,2019
stripe/api_resources/event.py,sha256=LTGYo3RBwycMjLwfDcW8QeHgs5wyaiRy4KXNNv5PDLM,190
stripe/api_resources/exchange_rate.py,sha256=gWN3Z0FIh7p139emwdjIYwcKd6yaRyQM2m1GNd3ZIaU,205
stripe/api_resources/file.py,sha256=F-977kVKUV_USKZz8oC4-nVt8KpO_bwXceaSO7pYihQ,1539
stripe/api_resources/file_link.py,sha256=dTQuuH2TLxZdss1wNzJmtHwQPI6ZbiJ0bP8UI2zZ46U,377
stripe/api_resources/invoice.py,sha256=0k9ScluNI06chtBHvpzrzeMJubeNgOoMXaNKQOEgVN0,2646
stripe/api_resources/invoice_item.py,sha256=WlnfEs9V4_Ah9mRNb-dLC4UTwBYd6cpJ4wMq7QjllqU,480
stripe/api_resources/invoice_line_item.py,sha256=NN_adyiRVQSje6FJ02ClyBjfqJviL82DY93sHe04xGo,181
stripe/api_resources/issuer_fraud_record.py,sha256=vnCzCW9KFxW9OBpWNbUvQ8F-0x312UU6pNiL5ofghZ4,216
stripe/api_resources/issuing/__init__.py,sha256=qxIugN5_qUV1-8vN_CRUQzurKZ6yyFH28X4CZfe8Q1U,453
stripe/api_resources/issuing/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/authorization.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/card.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/card_details.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/cardholder.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/dispute.cpython-310.pyc,,
stripe/api_resources/issuing/__pycache__/transaction.cpython-310.pyc,,
stripe/api_resources/issuing/authorization.py,sha256=zhIEYvO7t7t90OaUgwm-LKEi_bTQxkYE9L3WDmIq7GI,969
stripe/api_resources/issuing/card.py,sha256=2yKH1YxXZOd6fDUbdeLfU6eO3E83QDaXSxcelzwIwVA,602
stripe/api_resources/issuing/card_details.py,sha256=9Dk9-CcRt99iMDmsytjtRqqi63irsCPDcENBWJ52es4,188
stripe/api_resources/issuing/cardholder.py,sha256=xh5RI6yYE5KS14uMuYoIlKu0dRvEphlx-OXOU8D-6AM,388
stripe/api_resources/issuing/dispute.py,sha256=YeI6YSDDjJZGz4JPGEYVnLINi2bIMYaVpPyuwB8ZdAE,753
stripe/api_resources/issuing/transaction.py,sha256=FUpAOG8AxJk4fG97HR-Wqpnn57BSRk9Y68N0rhp3PVM,297
stripe/api_resources/line_item.py,sha256=jZXcxwnjSNOTd77jnnLl5Turdrcdo1CfKCkJwhZSOWU,176
stripe/api_resources/list_object.py,sha256=_9_0E7rLs60Ijtrgnjgue-8k1WQHKUJFG52b9USe3Z8,5374
stripe/api_resources/login_link.py,sha256=UEP_XZKZoHgMR5ELPMY5lEIcHx4L3poIe-Xdd_DpmOs,176
stripe/api_resources/mandate.py,sha256=Pm1uC9d_Lu4LWjCa8V1bhLoNW9DimEwxAWLVH5jVKWE,178
stripe/api_resources/order.py,sha256=lnigB7TBb60ikJkJFrM84S2UbHvq5Ig5E3Z0oo2_EO4,1023
stripe/api_resources/order_return.py,sha256=KnH2fAP0lh21E9EVhKQaT_5JOVILkLK1nineRrudfNY,203
stripe/api_resources/payment_intent.py,sha256=r5-s15Wfg4B7eSb9sFjbyHIjejXifVdEdrAs3BJow6I,1346
stripe/api_resources/payment_method.py,sha256=MKrYpPRztQgpDFnW9hLMAIS34G6Hettd9wzXI2EycWU,1049
stripe/api_resources/payout.py,sha256=Oiy9ze7sYV79-KNVX6ykj0eDAyNmS5W6wHnxumR455E,1037
stripe/api_resources/person.py,sha256=Ckd0Ul0TrGaxy2vgcNWaEjBWLwxaqAlnDV6lpAzhcX4,1050
stripe/api_resources/plan.py,sha256=FuE3BCjdBH1TSSOWt5aZv2CPCRCrWPGnJx5HJqhpCRg,466
stripe/api_resources/price.py,sha256=ygWLTsu39DcOKKkwWlY8HJnv-e1YTXkx95ObJGHUOYM,364
stripe/api_resources/product.py,sha256=W28RxWaOLxtqtzmuh40Oy5XNrVn6YODFWE6xjfS1RNY,472
stripe/api_resources/promotion_code.py,sha256=JmIb3BYJsUWacDI-1ahw3DRcd1m0uylv4VupJmDmJ8c,387
stripe/api_resources/radar/__init__.py,sha256=ju_hueFrOO8BwAz1_DaDP4qcEKlEw22DTbbB_J8p9dY,288
stripe/api_resources/radar/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/radar/__pycache__/early_fraud_warning.cpython-310.pyc,,
stripe/api_resources/radar/__pycache__/value_list.cpython-310.pyc,,
stripe/api_resources/radar/__pycache__/value_list_item.cpython-310.pyc,,
stripe/api_resources/radar/early_fraud_warning.py,sha256=88fvsvRsUIb2K67tqLfycYGij_aHFd3i8SrHLcUpp9o,222
stripe/api_resources/radar/value_list.py,sha256=pIAHH44XgOQ9TlN1zvp7ldDvHHLMMoaxRXVXSAyk16Y,483
stripe/api_resources/radar/value_list_item.py,sha256=t5RSHJRmSZ2tTjI2Ph6Uk7uGgYSM-uukCX5Y6tVBjMY,392
stripe/api_resources/recipient.py,sha256=vT81dheabBWZ1JN1Cpw__dwRhO0NxuZpGH4ywhR51I0,476
stripe/api_resources/recipient_transfer.py,sha256=oj4znEhPC0Y4vp8hkUpyNwvEfBvVyUowsOLO4z-nHbQ,271
stripe/api_resources/refund.py,sha256=4xic8DfTHPxhAqnFMyjOd9usJirUlijvWXTuPO3wwOg,372
stripe/api_resources/reporting/__init__.py,sha256=Wd3IliWFmAF-3tWSiZc6Ne5tdwZgSY6t67FOPs3mXeI,212
stripe/api_resources/reporting/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/reporting/__pycache__/report_run.cpython-310.pyc,,
stripe/api_resources/reporting/__pycache__/report_type.cpython-310.pyc,,
stripe/api_resources/reporting/report_run.py,sha256=WGiafbe6SxuJTUqtbtbC9ORJ6tIzjKvcArXUQoZZjC4,296
stripe/api_resources/reporting/report_type.py,sha256=OZRzpA072ZDKeMCh-qf3Do1l-T6c1f8vFuV26t0VssQ,211
stripe/api_resources/reversal.py,sha256=_eVZ9mQ9Ag2ZbsLpO13HYly1A8mQu2ZUlrrgVWJBsTw,1085
stripe/api_resources/review.py,sha256=5v45jhsBc7Qm1SqwuBIH0Swf7gFe0ukNLEEeOoUIHyQ,566
stripe/api_resources/setup_attempt.py,sha256=1meFqi9VNqOV_eGYbbjz0fNNtynOiChUHVgInrsjm4Y,205
stripe/api_resources/setup_intent.py,sha256=fEh9ByL8EdDUF3w6no2M4mrYkCOxQuJTrQCvKuXaAJY,1048
stripe/api_resources/sigma/__init__.py,sha256=6Jli3Vjwkqbx6NNQdcJOydRzMoRrlfGWhTuFX7bwXT0,159
stripe/api_resources/sigma/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/sigma/__pycache__/scheduled_query_run.cpython-310.pyc,,
stripe/api_resources/sigma/scheduled_query_run.py,sha256=QORDZ79m6FQpGxySXUwWVQx2k9nV-KQNjimLFl59wMo,306
stripe/api_resources/sku.py,sha256=9kxzRNBTEnaHTy2Jz8nOUrFrWgVb4CHV33mNzMCEjiE,464
stripe/api_resources/source.py,sha256=vzqtuj-ZLIjj2TE0gPx4JdvJqTbKfV3B8A2N4jNRZdQ,1650
stripe/api_resources/source_transaction.py,sha256=3rgyEe-HVc_-XDlWyoOfUqknsdSJ9n6RRCZsLW_LHes,192
stripe/api_resources/subscription.py,sha256=KMR_cWX_m3xbWZ0Grq4MlrhvnXxG6oEUpIZx0v_PpcM,1030
stripe/api_resources/subscription_item.py,sha256=SGNZHirMOFXhOurOp49pPzDtBWSoLqAc6Cdk2ZJ17c4,1039
stripe/api_resources/subscription_schedule.py,sha256=mkzqiy32wxShMCflSG3DJLEgWWq8Xm1NlD1h5HKSieA,1066
stripe/api_resources/tax_id.py,sha256=IB18k4FQZZACXuav9UyMdtZz5jp58o_B3o6txyLPXbY,795
stripe/api_resources/tax_rate.py,sha256=Pw7Isk2aZ8AlkiTUlZoaeOfKc89_8SyZ_Jz8JWWpFIk,375
stripe/api_resources/terminal/__init__.py,sha256=V8mNCtK7MTaMG111ABWHPfKFQ4Suq69SFES6_iwIn2A,273
stripe/api_resources/terminal/__pycache__/__init__.cpython-310.pyc,,
stripe/api_resources/terminal/__pycache__/connection_token.cpython-310.pyc,,
stripe/api_resources/terminal/__pycache__/location.cpython-310.pyc,,
stripe/api_resources/terminal/__pycache__/reader.cpython-310.pyc,,
stripe/api_resources/terminal/connection_token.py,sha256=8IKqhfC73YqnEIF3WR8xvkM39FVFjfJt-u3vgmVzqlc,224
stripe/api_resources/terminal/location.py,sha256=hEwYSq0LGDoFIb4ArrNOTFgCk_G7sFoSV6uWGfQD6d4,483
stripe/api_resources/terminal/reader.py,sha256=YIQir8pbRdziQFzGdxCa84BBWTEg5EPlm67xBxsBVUo,479
stripe/api_resources/three_d_secure.py,sha256=87-RgrIu8aTCs5TA5BJX4W1dmvWTLj97Bs-8Gj11xGQ,283
stripe/api_resources/token.py,sha256=o-f54__eJz8q2qKKsPkCovGR3jbGpWFvswDPydrbDgs,194
stripe/api_resources/topup.py,sha256=PkKnGyFxVy-OkORJeC4oKOS6vYgQj88Li-KB7DNuIH0,735
stripe/api_resources/transfer.py,sha256=kqWrdLeMQXyY-ra2astdFFYqJVZwdWhEVdwRo8SFUOk,921
stripe/api_resources/usage_record.py,sha256=RwWsrM4jY17CeqaLpn4l-tbHzaEUpHmQPCVOambe-yg,1059
stripe/api_resources/usage_record_summary.py,sha256=-GB-Dq3C_4cUs1kLxwxLB7YM6mBvTw63rWExmASQzmc,195
stripe/api_resources/webhook_endpoint.py,sha256=IAVa4qRHvohA9t4EOBF7ML_U7a9rvBwiVvEcK6fuPfQ,489
stripe/data/ca-certificates.crt,sha256=hmlbG-kiXDz4gtKD8FyUTjqrvB32QopEJCaak-mX3GU,209309
stripe/error.py,sha256=GqsTN5A9NRrwEwqDdxls7u-4csWIbW6XkbsWBohGfOU,4284
stripe/http_client.py,sha256=z6wqXlE1oC2IVh0S8X2nkJfWU7LHQ_oAt45Vy4sP_zI,22871
stripe/multipart_data_generator.py,sha256=gRAysPugFYWRbN85egjQuHquzIRnj0nOGMpWRjuthaA,2745
stripe/oauth.py,sha256=T3DU2no1-1wYQyBz8RtnMwX1u5b6q7Dr8NtpLbWlV0Q,2027
stripe/oauth_error.py,sha256=vEWQvMoqkb4Z4tHJTZZvXftSEqorm_XkkIYem-OFwWA,1022
stripe/object_classes.py,sha256=dBFQGoLxghK95YTER7vtXrYkw_K9iNy43hgVAMiwhvo,6331
stripe/request_metrics.py,sha256=FHW4rRRYmUe54IWlEgC7BIsM7yvEXMBncPut0E0EWGY,401
stripe/six.py,sha256=mfuCmJVc-Xc5C8DuPMNTQO2uZxjCHoFLCdguEd_cNBM,32463
stripe/stripe_object.py,sha256=CHlXB9zXcCVCRL0Jj2ugDW2eb-UwFly7eQjJcHVKPvg,11409
stripe/stripe_response.py,sha256=6cLvPMxeSTik1cR3s7EV1cQRptR-IFh3X0-lfjA5RzY,648
stripe/util.py,sha256=eeqxrOrHk2YCn4_Wd6uLW1Mzo00L4QN-EPidnDoyh9Y,6925
stripe/version.py,sha256=sJ6grV1Hhrz18KijW8Xr1zRCs_LAsgjN2wQrh8ohLeE,19
stripe/webhook.py,sha256=pkgwC3JVz87avSG5bmV0M6zww2RhY3rG2rxW03GLdqo,2720
