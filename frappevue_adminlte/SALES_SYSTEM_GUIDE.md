# دليل نظام إدارة المبيعات

## نظرة عامة
تم تطوير نظام إدارة المبيعات ليتيح لك إنشاء وإدارة فواتير المبيعات بسهولة من خلال واجهة ويب حديثة ومتجاوبة.

## الميزات الجديدة

### 1. إنشاء فاتورة مبيعات جديدة
- **المسار**: `/create-invoice`
- **الوصول**: من خلال قائمة "الفواتير" في الشريط العلوي أو زر "إنشاء فاتورة جديدة" في صفحة قائمة الفواتير
- **الميزات**:
  - اختيار العميل من قائمة منسدلة
  - إضافة تاريخ الفاتورة وتاريخ الاستحقاق
  - اختيار العملة (ريال سعودي، دولار أمريكي، يورو)
  - إضافة أصناف متعددة مع الكميات والأسعار
  - حساب تلقائي للمجاميع وضريبة القيمة المضافة (15%)
  - التحقق من صحة البيانات قبل الحفظ

### 2. إضافة عميل جديد
- **المسار**: `/create-customer`
- **الوصول**: من خلال زر "إضافة عميل" في صفحة إنشاء الفاتورة أو قائمة "الفواتير"
- **الميزات**:
  - إدخال بيانات العميل الأساسية (الاسم، النوع، البريد الإلكتروني، الجوال)
  - إضافة الرقم الضريبي والمنطقة
  - إدخال عنوان مفصل
  - إنشاء تلقائي لسجلات العنوان والاتصال المرتبطة

### 3. تحسينات على قائمة الفواتير
- **المسار**: `/my-invoices`
- **التحسينات**:
  - واجهة باللغة العربية
  - زر سريع لإنشاء فاتورة جديدة
  - تنسيق محسن للجدول

### 4. شريط التنقل المحسن
- قائمة منسدلة للفواتير تشمل:
  - قائمة الفواتير
  - إنشاء فاتورة جديدة
  - إضافة عميل جديد
- قائمة المستخدم مع خيارات الملف الشخصي وتسجيل الخروج

## كيفية الاستخدام

### إنشاء فاتورة مبيعات جديدة:

1. **الوصول للصفحة**:
   - انقر على "الفواتير" في الشريط العلوي
   - اختر "إنشاء فاتورة جديدة"

2. **ملء بيانات الفاتورة**:
   - اختر العميل من القائمة المنسدلة
   - إذا لم يكن العميل موجوداً، انقر على "إضافة عميل" لإنشاء عميل جديد
   - حدد تاريخ الفاتورة (افتراضياً اليوم)
   - حدد تاريخ الاستحقاق (اختياري)
   - اختر العملة

3. **إضافة الأصناف**:
   - اختر الصنف من القائمة المنسدلة
   - أدخل الكمية
   - أدخل السعر (سيتم تعبئته تلقائياً إذا كان محدد للصنف)
   - سيتم حساب المبلغ تلقائياً
   - لإضافة صنف آخر، انقر على "إضافة صنف"
   - لحذف صنف، انقر على أيقونة سلة المهملات

4. **مراجعة المجاميع**:
   - سيتم حساب المجموع الفرعي تلقائياً
   - سيتم إضافة ضريبة القيمة المضافة (15%)
   - سيظهر المجموع الكلي

5. **حفظ الفاتورة**:
   - انقر على "حفظ الفاتورة"
   - ستظهر رسالة تأكيد مع رقم الفاتورة
   - سيتم توجيهك لصفحة تفاصيل الفاتورة

### إضافة عميل جديد:

1. **الوصول للصفحة**:
   - من صفحة إنشاء الفاتورة، انقر على "إضافة عميل"
   - أو من قائمة "الفواتير" اختر "إضافة عميل جديد"

2. **ملء بيانات العميل**:
   - أدخل اسم العميل (مطلوب)
   - اختر نوع العميل (فرد أو شركة)
   - أدخل البريد الإلكتروني والجوال (اختياري)
   - أدخل الرقم الضريبي إذا كان متوفراً
   - اختر المنطقة

3. **إضافة العنوان** (اختياري):
   - أدخل العنوان الأول والثاني
   - أدخل المدينة والمنطقة والرمز البريدي

4. **حفظ العميل**:
   - انقر على "حفظ العميل"
   - ستظهر رسالة تأكيد
   - سيتم إرجاعك للصفحة السابقة

## الدعم التقني

### متطلبات النظام:
- Frappe Framework
- Vue.js 3
- Node.js
- متصفح حديث يدعم ES6+

### إعدادات API:
- تأكد من أن متغير `VUE_APP_FRAPPE_DOMAIN` محدد في ملف `.env`
- تأكد من أن إعدادات CORS مفعلة في Frappe

### استكشاف الأخطاء:
1. **خطأ في تحميل البيانات**: تأكد من الاتصال بخادم Frappe
2. **خطأ في الحفظ**: تأكد من صلاحيات المستخدم لإنشاء المستندات
3. **مشاكل في العرض**: تأكد من تحديث المتصفح وتفعيل JavaScript

## التطوير المستقبلي

### ميزات مخطط إضافتها:
- تعديل الفواتير الموجودة
- طباعة الفواتير
- تصدير الفواتير لـ PDF
- إدارة المخزون
- تقارير المبيعات
- إشعارات تلقائية للعملاء

### تحسينات مقترحة:
- إضافة المزيد من العملات
- دعم الخصومات
- إضافة ضرائب مخصصة
- تكامل مع أنظمة الدفع الإلكتروني

---

**ملاحظة**: هذا النظام تم تطويره خصيصاً للعمل مع Frappe Framework ويتطلب إعداد صحيح للخادم والصلاحيات.
