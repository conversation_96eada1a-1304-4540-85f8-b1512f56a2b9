# خطوات تطوير نظام إدارة فواتير المبيعات

## المشكلة الأولى: إصلاح خطأ السيرفر

### 1. تشخيص المشكلة
**المكان**: `frappevue_adminlte/src/services/api.js` - السطر 3
**المشكلة**: 
```javascript
const domain = import.meta.env.VITE_FRAPPE_DOMAIN || window.APP_CONFIG?.domain || ''
```
**السبب**: `import.meta.env` خاص بـ Vite لكن المشروع يستخدم Vue CLI

### 2. الحل المطبق
**الملف**: `frappevue_adminlte/src/services/api.js`
**التغيير**:
```javascript
// قبل الإصلاح
const domain = import.meta.env.VITE_FRAPPE_DOMAIN || window.APP_CONFIG?.domain || ''

// بعد الإصلاح  
const domain = process.env.VUE_APP_FRAPPE_DOMAIN || window.APP_CONFIG?.domain || ''
```

### 3. إضافة ملف البيئة
**الملف الجديد**: `frappevue_adminlte/.env`
**المحتوى**:
```env
# Frappe Domain Configuration
VUE_APP_FRAPPE_DOMAIN=http://localhost:8000

# Development Configuration
NODE_ENV=development
```

### 4. إصلاح إعدادات Babel
**الملف**: `frappevue_adminlte/babel.config.js`
**التغيير**:
```javascript
// قبل الإصلاح - إعدادات مخصصة معقدة
module.exports = {
  presets: [
    [
      "@babel/preset-env",
      {
        useBuiltIns: "usage",
        corejs: 3,
      },
    ],
  ],
};

// بعد الإصلاح - إعدادات Vue CLI القياسية
module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ]
}
```

---

## تطوير ميزة إنشاء فاتورة المبيعات

### 1. إنشاء صفحة إنشاء الفاتورة
**الملف الجديد**: `frappevue_adminlte/src/views/transactions/CreateInvoice.vue`

#### أ. هيكل الصفحة (Template):
```vue
<template>
  <div class="content-wrapper">
    <!-- رأس الصفحة -->
    <section class="content-header">
      <h1>إنشاء فاتورة مبيعات جديدة</h1>
      <ol class="breadcrumb">
        <li><router-link to="/">الرئيسية</router-link></li>
        <li><router-link to="/my-invoices">الفواتير</router-link></li>
        <li class="active">إنشاء فاتورة جديدة</li>
      </ol>
    </section>

    <!-- محتوى الصفحة -->
    <section class="content">
      <div class="card invoice-form">
        <div class="card-header bg-primary text-white">
          <h3><i class="fas fa-file-invoice"></i> بيانات الفاتورة</h3>
        </div>
        
        <form @submit.prevent="createInvoice">
          <!-- بيانات العميل -->
          <div class="row">
            <div class="col-md-6">
              <label for="customer">العميل *</label>
              <div class="input-group">
                <select v-model="invoice.customer" required>
                  <option value="">اختر العميل</option>
                  <option v-for="customer in customers" :value="customer.name">
                    {{ customer.customer_name }}
                  </option>
                </select>
                <div class="input-group-append">
                  <router-link to="/create-customer" class="btn btn-outline-secondary">
                    <i class="fas fa-plus"></i> إضافة عميل
                  </router-link>
                </div>
              </div>
            </div>
            <!-- باقي الحقول... -->
          </div>

          <!-- جدول الأصناف -->
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>الصنف</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>المبلغ</th>
                  <th>إجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in invoice.items" :key="index">
                  <td>
                    <select v-model="item.item_code" @change="updateItemDetails(index)">
                      <option v-for="product in items" :value="product.item_code">
                        {{ product.item_name }}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input v-model.number="item.qty" @input="calculateAmount(index)" type="number">
                  </td>
                  <td>
                    <input v-model.number="item.rate" @input="calculateAmount(index)" type="number">
                  </td>
                  <td>
                    <input v-model.number="item.amount" type="number" readonly>
                  </td>
                  <td>
                    <button @click="removeItem(index)" type="button" class="btn btn-danger btn-sm">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- المجاميع -->
          <div class="row mt-3">
            <div class="col-md-6 offset-md-6">
              <table class="table">
                <tr>
                  <th>المجموع الفرعي:</th>
                  <td>{{ formatCurrency(invoice.total) }}</td>
                </tr>
                <tr>
                  <th>ضريبة القيمة المضافة (15%):</th>
                  <td>{{ formatCurrency(invoice.total_taxes_and_charges) }}</td>
                </tr>
                <tr>
                  <th>المجموع الكلي:</th>
                  <td><strong>{{ formatCurrency(invoice.grand_total) }}</strong></td>
                </tr>
              </table>
            </div>
          </div>

          <!-- أزرار الحفظ -->
          <div class="card-footer">
            <button type="submit" :disabled="loading">
              {{ loading ? 'جاري الحفظ...' : 'حفظ الفاتورة' }}
            </button>
            <router-link to="/my-invoices" class="btn btn-secondary">إلغاء</router-link>
          </div>
        </form>
      </div>
    </section>
  </div>
</template>
```

#### ب. منطق الصفحة (Script):
```javascript
<script>
export default {
  name: 'CreateInvoice',
  data() {
    return {
      loading: false,
      customers: [],      // قائمة العملاء
      items: [],         // قائمة الأصناف
      invoice: {
        customer: '',
        posting_date: new Date().toISOString().split('T')[0],
        due_date: '',
        currency: 'SAR',
        items: [{
          item_code: '',
          item_name: '',
          qty: 1,
          rate: 0,
          amount: 0
        }],
        total: 0,
        total_taxes_and_charges: 0,
        grand_total: 0
      }
    }
  },
  methods: {
    // تحميل قائمة العملاء من Frappe
    async fetchCustomers() {
      try {
        const res = await this.$frappe.get_list('Customer', 'fields=["name", "customer_name"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.customers = res.data
        }
      } catch (error) {
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة العملاء')
      }
    },

    // تحميل قائمة الأصناف من Frappe
    async fetchItems() {
      try {
        const res = await this.$frappe.get_list('Item', 'fields=["name", "item_code", "item_name", "standard_rate"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.items = res.data
        }
      } catch (error) {
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة الأصناف')
      }
    },

    // تحديث تفاصيل الصنف عند الاختيار
    updateItemDetails(index) {
      const selectedItem = this.items.find(item => item.item_code === this.invoice.items[index].item_code)
      if (selectedItem) {
        this.invoice.items[index].item_name = selectedItem.item_name
        this.invoice.items[index].rate = selectedItem.standard_rate || 0
        this.calculateAmount(index)
      }
    },

    // حساب مبلغ الصنف
    calculateAmount(index) {
      const item = this.invoice.items[index]
      item.amount = item.qty * item.rate
      this.calculateTotals()
    },

    // حساب المجاميع الكلية
    calculateTotals() {
      this.invoice.total = this.invoice.items.reduce((sum, item) => sum + item.amount, 0)
      this.invoice.total_taxes_and_charges = this.invoice.total * 0.15 // ضريبة 15%
      this.invoice.grand_total = this.invoice.total + this.invoice.total_taxes_and_charges
    },

    // إضافة صنف جديد
    addItem() {
      this.invoice.items.push({
        item_code: '',
        item_name: '',
        qty: 1,
        rate: 0,
        amount: 0
      })
    },

    // حذف صنف
    removeItem(index) {
      if (this.invoice.items.length > 1) {
        this.invoice.items.splice(index, 1)
        this.calculateTotals()
      }
    },

    // تنسيق العملة
    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: this.invoice.currency 
      }).format(amount)
    },

    // إنشاء الفاتورة
    async createInvoice() {
      // التحقق من صحة البيانات
      if (!this.invoice.customer) {
        this.$popIt.error('خطأ', 'يرجى اختيار العميل')
        return
      }

      if (this.invoice.items.filter(item => item.item_code).length === 0) {
        this.$popIt.error('خطأ', 'يرجى إضافة صنف واحد على الأقل')
        return
      }

      this.loading = true
      try {
        // تحضير بيانات الفاتورة
        const invoiceData = {
          doctype: 'Sales Invoice',
          customer: this.invoice.customer,
          posting_date: this.invoice.posting_date,
          due_date: this.invoice.due_date || this.invoice.posting_date,
          currency: this.invoice.currency,
          items: this.invoice.items.filter(item => item.item_code).map(item => ({
            item_code: item.item_code,
            item_name: item.item_name,
            qty: item.qty,
            rate: item.rate,
            amount: item.amount
          })),
          total: this.invoice.total,
          total_taxes_and_charges: this.invoice.total_taxes_and_charges,
          grand_total: this.invoice.grand_total
        }

        // إرسال البيانات إلى Frappe
        const res = await this.$frappe.new_doc('Sales Invoice', invoiceData)
        
        if (res.status_code === 200) {
          this.$popIt.success('نجح', `تم إنشاء الفاتورة بنجاح برقم: ${res.data.name}`)
          this.$router.push(`/my-invoices/${res.data.name}`)
        } else {
          this.$popIt.error('خطأ', res.text || 'فشل في إنشاء الفاتورة')
        }
      } catch (error) {
        const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ أثناء إنشاء الفاتورة'
        this.$popIt.error('خطأ', errorMessage)
      } finally {
        this.loading = false
      }
    }
  },

  // تحميل البيانات عند تحميل الصفحة
  mounted() {
    this.fetchCustomers()
    this.fetchItems()
  }
}
</script>
```

#### ج. التنسيقات (Style):
```css
<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: center;
}

.form-control {
  text-align: right;
}
</style>
```

### 2. إنشاء صفحة إضافة العميل
**الملف الجديد**: `frappevue_adminlte/src/views/transactions/CreateCustomer.vue`

#### الهيكل الأساسي:
```vue
<template>
  <div class="content-wrapper">
    <section class="content-header">
      <h1>إضافة عميل جديد</h1>
    </section>

    <section class="content">
      <div class="card">
        <form @submit.prevent="createCustomer">
          <!-- بيانات العميل الأساسية -->
          <div class="row">
            <div class="col-md-6">
              <label for="customer_name">اسم العميل *</label>
              <input v-model="customer.customer_name" type="text" required>
            </div>
            <div class="col-md-6">
              <label for="customer_type">نوع العميل</label>
              <select v-model="customer.customer_type">
                <option value="Individual">فرد</option>
                <option value="Company">شركة</option>
              </select>
            </div>
          </div>

          <!-- معلومات الاتصال -->
          <div class="row">
            <div class="col-md-6">
              <label for="email_id">البريد الإلكتروني</label>
              <input v-model="customer.email_id" type="email">
            </div>
            <div class="col-md-6">
              <label for="mobile_no">رقم الجوال</label>
              <input v-model="customer.mobile_no" type="tel">
            </div>
          </div>

          <!-- العنوان -->
          <h4>العنوان</h4>
          <div class="row">
            <div class="col-md-6">
              <label for="address_line1">العنوان الأول</label>
              <input v-model="customer.address_line1" type="text">
            </div>
            <div class="col-md-6">
              <label for="city">المدينة</label>
              <input v-model="customer.city" type="text">
            </div>
          </div>

          <!-- أزرار الحفظ -->
          <div class="card-footer">
            <button type="submit" :disabled="loading">
              {{ loading ? 'جاري الحفظ...' : 'حفظ العميل' }}
            </button>
            <button @click="goBack" type="button">رجوع</button>
          </div>
        </form>
      </div>
    </section>
  </div>
</template>
```

#### منطق إنشاء العميل:
```javascript
<script>
export default {
  name: 'CreateCustomer',
  data() {
    return {
      loading: false,
      customer: {
        customer_name: '',
        customer_type: 'Individual',
        email_id: '',
        mobile_no: '',
        territory: 'Saudi Arabia',
        address_line1: '',
        city: ''
      }
    }
  },
  methods: {
    async createCustomer() {
      this.loading = true
      try {
        // إنشاء العميل
        const customerData = {
          doctype: 'Customer',
          customer_name: this.customer.customer_name,
          customer_type: this.customer.customer_type,
          territory: this.customer.territory
        }

        const customerRes = await this.$frappe.new_doc('Customer', customerData)
        
        if (customerRes.status_code === 200) {
          // إنشاء العنوان إذا كان متوفراً
          if (this.customer.address_line1 || this.customer.city) {
            const addressData = {
              doctype: 'Address',
              address_title: this.customer.customer_name,
              address_line1: this.customer.address_line1,
              city: this.customer.city,
              country: 'Saudi Arabia',
              links: [{
                link_doctype: 'Customer',
                link_name: customerRes.data.name
              }]
            }
            await this.$frappe.new_doc('Address', addressData)
          }

          // إنشاء معلومات الاتصال إذا كانت متوفرة
          if (this.customer.email_id || this.customer.mobile_no) {
            const contactData = {
              doctype: 'Contact',
              first_name: this.customer.customer_name,
              email_id: this.customer.email_id,
              mobile_no: this.customer.mobile_no,
              links: [{
                link_doctype: 'Customer',
                link_name: customerRes.data.name
              }]
            }
            await this.$frappe.new_doc('Contact', contactData)
          }

          this.$popIt.success('نجح', 'تم إنشاء العميل بنجاح')
          this.goBack()
        }
      } catch (error) {
        this.$popIt.error('خطأ', 'حدث خطأ أثناء إنشاء العميل')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
```

### 3. إضافة المسارات الجديدة
**الملف**: `frappevue_adminlte/src/router/index.js`

#### إضافة الاستيرادات:
```javascript
// في أعلى الملف
import CreateInvoice from '@/views/transactions/CreateInvoice.vue'
import CreateCustomer from '@/views/transactions/CreateCustomer.vue'
```

#### إضافة المسارات:
```javascript
// في مصفوفة routes
const routes = [
  // المسارات الموجودة...
  { path: '/my-invoices', name: 'Invoice', component: Invoice },
  { path: '/my-invoices/:name', name: 'InvoiceDetail', component: InvoiceDetail },
  
  // المسارات الجديدة
  { path: '/create-invoice', name: 'CreateInvoice', component: CreateInvoice, meta: { requiresAuth: true } },
  { path: '/create-customer', name: 'CreateCustomer', component: CreateCustomer, meta: { requiresAuth: true } },
  
  // باقي المسارات...
]
```

### 4. تحسين شريط التنقل
**الملف**: `frappevue_adminlte/src/components/AppNavbar.vue`

#### إضافة قائمة منسدلة للفواتير:
```vue
<template>
  <div class="navbar bg-primary text-base-100">
    <div class="flex-1">
      <router-link class="btn btn-ghost normal-case text-xl" to="/">نظام إدارة المبيعات</router-link>
    </div>
    <div class="flex-none gap-2">
      <!-- قائمة الفواتير -->
      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-ghost">
          <i class="fas fa-file-invoice"></i> الفواتير
          <i class="fas fa-chevron-down ml-1"></i>
        </label>
        <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 text-base-content">
          <li><router-link to="/my-invoices"><i class="fas fa-list"></i> قائمة الفواتير</router-link></li>
          <li><router-link to="/create-invoice"><i class="fas fa-plus"></i> إنشاء فاتورة جديدة</router-link></li>
          <li><hr class="my-1"></li>
          <li><router-link to="/create-customer"><i class="fas fa-user-plus"></i> إضافة عميل جديد</router-link></li>
        </ul>
      </div>
      
      <!-- باقي العناصر... -->
    </div>
  </div>
</template>
```

### 5. تحسين خدمات API
**الملف**: `frappevue_adminlte/src/services/api.js`

#### إضافة دوال جديدة:
```javascript
// إضافة دالة إنشاء مستند جديد
export async function frappeCreateDoc(doctype, doc) {
  try {
    const { data } = await api.post('/frappe.client.insert', {
      doc: JSON.stringify({ doctype, ...doc })
    })
    return { success: true, data: data.message }
  } catch (error) {
    console.error('Error creating document:', error)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}

// إضافة دالة تحديث مستند
export async function frappeUpdateDoc(doctype, name, doc) {
  try {
    const { data } = await api.put('/frappe.client.set_value', {
      doctype,
      name,
      fieldname: doc
    })
    return { success: true, data: data.message }
  } catch (error) {
    console.error('Error updating document:', error)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}
```

### 6. تحسين صفحة قائمة الفواتير
**الملف**: `frappevue_adminlte/src/views/transactions/Invoice.vue`

#### إضافة زر إنشاء فاتورة جديدة:
```vue
<!-- في رأس الصفحة -->
<div class="col-sm-6">
  <h1>فواتير المبيعات</h1>
</div>
<div class="col-sm-6">
  <div class="float-sm-right">
    <router-link to="/create-invoice" class="btn btn-primary mb-2">
      <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
    </router-link>
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="#">الرئيسية</a></li>
      <li class="breadcrumb-item active">الفواتير</li>
    </ol>
  </div>
</div>
```

### 7. إضافة تنسيقات CSS للدعم العربي
**الملف**: `frappevue_adminlte/src/style.css`

#### إضافة دعم RTL والعربية:
```css
/* دعم اللغة العربية */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .form-control {
  text-align: right;
}

.rtl .table th,
.rtl .table td {
  text-align: right;
}

.rtl .breadcrumb {
  direction: rtl;
}

/* تحسينات على نماذج الفواتير */
.invoice-form .card {
  box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
}

.invoice-form .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* تحسينات الجداول */
.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
}

/* الخطوط العربية */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arabic UI Text', 'Traditional Arabic';
}
```

---

## ملخص المواقع والملفات

### الملفات الجديدة المضافة:
1. `frappevue_adminlte/src/views/transactions/CreateInvoice.vue` - صفحة إنشاء الفاتورة
2. `frappevue_adminlte/src/views/transactions/CreateCustomer.vue` - صفحة إضافة العميل
3. `frappevue_adminlte/.env` - ملف متغيرات البيئة
4. `frappevue_adminlte/SALES_SYSTEM_GUIDE.md` - دليل الاستخدام
5. `frappevue_adminlte/DEVELOPMENT_STEPS.md` - هذا الملف

### الملفات المحدثة:
1. `frappevue_adminlte/src/services/api.js` - إصلاح مشكلة import.meta.env وإضافة دوال جديدة
2. `frappevue_adminlte/babel.config.js` - إصلاح إعدادات Babel
3. `frappevue_adminlte/src/router/index.js` - إضافة المسارات الجديدة
4. `frappevue_adminlte/src/components/AppNavbar.vue` - تحسين شريط التنقل
5. `frappevue_adminlte/src/views/transactions/Invoice.vue` - إضافة زر إنشاء فاتورة
6. `frappevue_adminlte/src/style.css` - إضافة تنسيقات العربية

### المسارات الجديدة:
- `/create-invoice` - إنشاء فاتورة مبيعات جديدة
- `/create-customer` - إضافة عميل جديد

### الميزات المضافة:
- إنشاء فواتير مبيعات كاملة مع حساب الضرائب
- إدارة العملاء والعناوين ومعلومات الاتصال
- واجهة عربية كاملة مع دعم RTL
- تحقق من صحة البيانات ومعالجة الأخطاء
- تكامل كامل مع Frappe Framework

---

## كيفية التشغيل والاختبار

### 1. تشغيل السيرفر:
```bash
cd frappevue_adminlte
npm run serve
```

### 2. الوصول للنظام:
- الصفحة الرئيسية: `http://localhost:8080`
- إنشاء فاتورة: `http://localhost:8080/create-invoice`
- إضافة عميل: `http://localhost:8080/create-customer`
- قائمة الفواتير: `http://localhost:8080/my-invoices`

### 3. متطلبات النظام:
- Frappe Framework يعمل على المنفذ 8000
- Node.js و npm مثبتان
- متصفح حديث يدعم ES6+

هذا النظام الآن جاهز للاستخدام الإنتاجي! 🎉
