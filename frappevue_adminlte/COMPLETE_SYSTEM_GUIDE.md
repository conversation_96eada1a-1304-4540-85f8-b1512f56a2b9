# دليل النظام الشامل - نظام إدارة المبيعات والمخزون

## 🎯 نظرة عامة

تم تطوير نظام شامل لإدارة المبيعات والمخزون يتضمن:
- **إدارة الفواتير**: إنشاء وعرض فواتير المبيعات
- **إدارة العملاء**: إضافة وإدارة بيانات العملاء
- **إدارة الأصناف**: إضافة وإدارة المنتجات والخدمات
- **إدارة المخازن**: إدارة المخازن وحركات المخزون
- **التقارير**: تقارير شاملة للمخزون والمبيعات

---

## 🔧 إصلاح المشاكل التقنية

### المشكلة الأساسية المحلولة:
**الخطأ**: `import.meta.env is not defined`
**الحل**: تم تغيير `import.meta.env.VITE_FRAPPE_DOMAIN` إلى `process.env.VUE_APP_FRAPPE_DOMAIN`
**الملف**: `src/services/api.js`

### تحسينات API:
- إصلاح دالة `new_doc` لاستخدام `/api/method/frappe.client.insert`
- تحسين معالجة الاستجابات في `handleResponse`
- إضافة معالجة أفضل للأخطاء

---

## 📋 مديولات النظام

### 1. مديول المبيعات (Sales Module)

#### أ. إدارة الفواتير
**المسارات**:
- `/my-invoices` - قائمة الفواتير
- `/create-invoice` - إنشاء فاتورة جديدة
- `/my-invoices/:name` - تفاصيل الفاتورة

**الملفات**:
- `src/views/transactions/Invoice.vue` - قائمة الفواتير
- `src/views/transactions/CreateInvoice.vue` - إنشاء فاتورة
- `src/views/transactions/InvoiceDetail.vue` - تفاصيل الفاتورة

**الميزات**:
- اختيار العميل من قائمة منسدلة
- إضافة أصناف متعددة مع الكميات والأسعار
- حساب تلقائي للضرائب (15% ضريبة قيمة مضافة)
- حفظ الفاتورة في Frappe
- عرض تفاصيل الفاتورة مع إمكانية الطباعة

#### ب. إدارة العملاء
**المسارات**:
- `/create-customer` - إضافة عميل جديد

**الملفات**:
- `src/views/transactions/CreateCustomer.vue`

**الميزات**:
- إدخال بيانات العميل الأساسية
- إضافة معلومات الاتصال والعنوان
- إنشاء تلقائي لسجلات العنوان والاتصال في Frappe

### 2. مديول المخزون (Inventory Module)

#### أ. إدارة الأصناف
**المسارات**:
- `/items` - قائمة الأصناف
- `/create-item` - إضافة صنف جديد

**الملفات**:
- `src/views/inventory/ItemList.vue` - قائمة الأصناف
- `src/views/inventory/CreateItem.vue` - إضافة صنف

**الميزات**:
- عرض قائمة شاملة بالأصناف مع الكميات المتاحة
- إضافة أصناف جديدة مع جميع التفاصيل
- إعدادات المخزون والتسعير
- البحث والفلترة
- تفعيل/تعطيل الأصناف

#### ب. إدارة المخازن
**المسارات**:
- `/warehouses` - قائمة المخازن
- `/stock-entry` - حركة مخزون

**الملفات**:
- `src/views/inventory/WarehouseList.vue` - قائمة المخازن
- `src/views/inventory/StockEntry.vue` - حركات المخزون

**الميزات**:
- عرض قائمة المخازن مع الإحصائيات
- إنشاء حركات مخزون (استلام، صرف، نقل)
- عرض قيمة المخزون لكل مخزن
- إحصائيات سريعة للمخزون

#### ج. التقارير
**المسارات**:
- `/stock-report/:warehouse?` - تقرير المخزون

**الملفات**:
- `src/views/inventory/StockReport.vue`

**الميزات**:
- تقرير شامل لحالة المخزون
- فلترة حسب المخزن ومجموعة الأصناف
- إحصائيات الأصناف المتوفرة والمنخفضة والنافدة
- إمكانية الطباعة والتصدير

---

## 🗂️ هيكل الملفات

### الملفات الجديدة المضافة:
```
frappevue_adminlte/
├── src/
│   ├── views/
│   │   ├── transactions/
│   │   │   ├── CreateInvoice.vue      # إنشاء فاتورة مبيعات
│   │   │   └── CreateCustomer.vue     # إضافة عميل جديد
│   │   └── inventory/
│   │       ├── ItemList.vue           # قائمة الأصناف
│   │       ├── CreateItem.vue         # إضافة صنف جديد
│   │       ├── WarehouseList.vue      # قائمة المخازن
│   │       ├── StockEntry.vue         # حركات المخزون
│   │       └── StockReport.vue        # تقرير المخزون
│   └── components/
│       └── AppNavbar.vue              # شريط التنقل المحسن
├── .env                               # متغيرات البيئة
├── SALES_SYSTEM_GUIDE.md             # دليل نظام المبيعات
├── DEVELOPMENT_STEPS.md              # خطوات التطوير
└── COMPLETE_SYSTEM_GUIDE.md          # هذا الملف
```

### الملفات المحدثة:
- `src/services/api.js` - إصلاح API وإضافة دوال جديدة
- `src/utils.js` - إصلاح دالة إنشاء المستندات
- `src/router/index.js` - إضافة المسارات الجديدة
- `src/style.css` - إضافة تنسيقات العربية
- `src/views/transactions/Invoice.vue` - تحسين قائمة الفواتير
- `babel.config.js` - إصلاح إعدادات Babel

---

## 🛣️ خريطة المسارات

### مسارات المبيعات:
- `/my-invoices` - قائمة فواتير المبيعات
- `/create-invoice` - إنشاء فاتورة مبيعات جديدة
- `/my-invoices/:name` - عرض تفاصيل فاتورة محددة
- `/create-customer` - إضافة عميل جديد

### مسارات المخزون:
- `/items` - قائمة الأصناف
- `/create-item` - إضافة صنف جديد
- `/warehouses` - قائمة المخازن
- `/stock-entry` - إنشاء حركة مخزون
- `/stock-report/:warehouse?` - تقرير المخزون (مع إمكانية تحديد مخزن)

---

## 🎨 واجهة المستخدم

### شريط التنقل:
- **قائمة المبيعات**: الفواتير، إنشاء فاتورة، إضافة عميل
- **قائمة المخزون**: الأصناف، المخازن، حركات المخزون، التقارير
- **قائمة المستخدم**: الملف الشخصي، تسجيل الخروج

### التصميم:
- دعم كامل للغة العربية مع RTL
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان متناسقة ومريحة للعين
- أيقونات واضحة ومعبرة

---

## 🔌 تكامل Frappe

### المستندات المستخدمة:
- **Sales Invoice** - فواتير المبيعات
- **Customer** - العملاء
- **Address** - عناوين العملاء
- **Contact** - معلومات اتصال العملاء
- **Item** - الأصناف
- **Item Group** - مجموعات الأصناف
- **UOM** - وحدات القياس
- **Warehouse** - المخازن
- **Stock Entry** - حركات المخزون
- **Bin** - أرصدة المخزون

### API المستخدمة:
- `frappe.client.get_list` - جلب قوائم البيانات
- `frappe.client.get` - جلب مستند محدد
- `frappe.client.insert` - إنشاء مستند جديد
- `frappe.client.set_value` - تحديث قيم المستند

---

## 🚀 كيفية الاستخدام

### 1. تشغيل النظام:
```bash
cd frappevue_adminlte
npm run serve
```

### 2. الوصول للنظام:
- الرابط: `http://localhost:8080`
- تسجيل الدخول بحساب Frappe صالح

### 3. إنشاء فاتورة مبيعات:
1. انقر على "المبيعات" → "إنشاء فاتورة جديدة"
2. اختر العميل (أو أضف عميل جديد)
3. أضف الأصناف مع الكميات والأسعار
4. راجع المجاميع والضرائب
5. احفظ الفاتورة

### 4. إدارة المخزون:
1. انقر على "المخزون" → "إدارة الأصناف"
2. أضف أصناف جديدة أو عدل الموجودة
3. أدر المخازن وحركات المخزون
4. راجع تقارير المخزون

---

## 🔧 متطلبات النظام

### الخادم:
- Frappe Framework v13+ يعمل على المنفذ 8000
- Python 3.7+
- MariaDB/MySQL

### العميل:
- Node.js 14+
- npm أو yarn
- متصفح حديث يدعم ES6+

### الإعدادات:
- متغير البيئة `VUE_APP_FRAPPE_DOMAIN` في ملف `.env`
- إعدادات CORS مفعلة في Frappe
- صلاحيات المستخدم لإنشاء وتعديل المستندات

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تحميل البيانات**:
   - تأكد من تشغيل خادم Frappe
   - تحقق من إعدادات CORS
   - تأكد من صحة رابط الخادم في `.env`

2. **خطأ في حفظ البيانات**:
   - تأكد من صلاحيات المستخدم
   - تحقق من صحة البيانات المدخلة
   - راجع سجلات الأخطاء في المتصفح

3. **مشاكل في العرض**:
   - تأكد من تحديث المتصفح
   - امسح ذاكرة التخزين المؤقت
   - تأكد من تفعيل JavaScript

---

## 🔮 التطوير المستقبلي

### ميزات مخطط إضافتها:
- **المبيعات**:
  - عروض الأسعار
  - أوامر البيع
  - إشعارات التسليم
  - المرتجعات

- **المخزون**:
  - جرد المخزون
  - تتبع الأرقام التسلسلية
  - إدارة انتهاء الصلاحية
  - تحسين مستويات المخزون

- **التقارير**:
  - تقارير المبيعات
  - تحليل الربحية
  - تقارير العملاء
  - لوحة معلومات تفاعلية

- **التحسينات**:
  - تطبيق جوال
  - إشعارات فورية
  - تكامل مع أنظمة الدفع
  - تصدير متقدم للبيانات

---

## 📞 الدعم التقني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء في المتصفح
3. راجع وثائق Frappe Framework
4. تواصل مع فريق التطوير

---

**تم تطوير هذا النظام بعناية ليوفر حلاً شاملاً لإدارة المبيعات والمخزون مع تكامل كامل مع Frappe Framework** 🎉
