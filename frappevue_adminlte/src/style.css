/* @tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .form-control {
  text-align: right;
}

.rtl .table th,
.rtl .table td {
  text-align: right;
}

.rtl .breadcrumb {
  direction: rtl;
}

.rtl .float-sm-right {
  float: left !important;
}

.rtl .ml-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

/* Custom styles for invoice forms */
.invoice-form .card {
  box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
}

.invoice-form .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.invoice-form .btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.invoice-form .btn-primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}

/* Table improvements */
.table-responsive {
  border-radius: 0.25rem;
}

.table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
}

/* Loading states */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Dropdown improvements */
.dropdown-content {
  z-index: 1000;
}

/* Arabic font improvements */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arabic UI Text', 'Traditional Arabic';
} */

/* Custom global styles */
body {
  font-family: sans-serif;
  background-color: #ffffff;
  color: #374151;
}