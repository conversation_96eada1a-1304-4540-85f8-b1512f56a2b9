<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>تقرير المخزون</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
              <li class="breadcrumb-item"><router-link to="/warehouses">المخازن</router-link></li>
              <li class="breadcrumb-item active">تقرير المخزون</li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- فلاتر التقرير -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">فلاتر التقرير</h3>
                <div class="card-tools">
                  <button @click="generateReport" class="btn btn-primary">
                    <i class="fas fa-search"></i> تشغيل التقرير
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="warehouse">المخزن</label>
                      <select v-model="filters.warehouse" class="form-control" id="warehouse">
                        <option value="">جميع المخازن</option>
                        <option v-for="warehouse in warehouses" :key="warehouse.name" :value="warehouse.name">
                          {{ warehouse.warehouse_name }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="item_group">مجموعة الأصناف</label>
                      <select v-model="filters.item_group" class="form-control" id="item_group">
                        <option value="">جميع المجموعات</option>
                        <option v-for="group in itemGroups" :key="group.name" :value="group.name">
                          {{ group.item_group_name }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="stock_status">حالة المخزون</label>
                      <select v-model="filters.stock_status" class="form-control" id="stock_status">
                        <option value="">جميع الحالات</option>
                        <option value="in_stock">متوفر</option>
                        <option value="low_stock">منخفض</option>
                        <option value="out_of_stock">نفد</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="search_item">البحث في الأصناف</label>
                      <input v-model="filters.search_item" type="text" class="form-control" id="search_item" placeholder="اسم أو كود الصنف">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
              <div class="inner">
                <h3>{{ totalItems }}</h3>
                <p>إجمالي الأصناف</p>
              </div>
              <div class="icon">
                <i class="fas fa-box"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
              <div class="inner">
                <h3>{{ inStockItems }}</h3>
                <p>أصناف متوفرة</p>
              </div>
              <div class="icon">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
              <div class="inner">
                <h3>{{ lowStockItems }}</h3>
                <p>أصناف منخفضة</p>
              </div>
              <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
              <div class="inner">
                <h3>{{ outOfStockItems }}</h3>
                <p>أصناف نفدت</p>
              </div>
              <div class="icon">
                <i class="fas fa-times-circle"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- جدول التقرير -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">تفاصيل المخزون</h3>
                <div class="card-tools">
                  <button @click="exportToExcel" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                  </button>
                  <button @click="printReport" class="btn btn-info btn-sm ml-1">
                    <i class="fas fa-print"></i> طباعة
                  </button>
                </div>
              </div>

              <div class="card-body table-responsive p-0" v-if="!loading">
                <table class="table table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>كود الصنف</th>
                      <th>اسم الصنف</th>
                      <th>المجموعة</th>
                      <th>المخزن</th>
                      <th>الكمية المتاحة</th>
                      <th>وحدة القياس</th>
                      <th>سعر التقييم</th>
                      <th>القيمة الإجمالية</th>
                      <th>مستوى إعادة الطلب</th>
                      <th>الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in filteredStockData" :key="`${item.item_code}-${item.warehouse}`">
                      <td>{{ item.item_code }}</td>
                      <td>{{ item.item_name }}</td>
                      <td>{{ item.item_group }}</td>
                      <td>{{ item.warehouse_name }}</td>
                      <td>
                        <span :class="getStockClass(item.actual_qty, item.reorder_level)">
                          {{ item.actual_qty || 0 }}
                        </span>
                      </td>
                      <td>{{ item.stock_uom }}</td>
                      <td>{{ formatCurrency(item.valuation_rate) }}</td>
                      <td>{{ formatCurrency(item.stock_value) }}</td>
                      <td>{{ item.reorder_level || 0 }}</td>
                      <td>
                        <span :class="getStatusBadgeClass(item.actual_qty, item.reorder_level)">
                          {{ getStockStatus(item.actual_qty, item.reorder_level) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="card-body text-center" v-if="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">جاري تحميل البيانات...</p>
              </div>

              <div class="card-footer">
                <div class="row">
                  <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                      عرض {{ filteredStockData.length }} من {{ stockData.length }} عنصر
                    </div>
                  </div>
                  <div class="col-sm-12 col-md-7">
                    <div class="text-right">
                      <strong>إجمالي قيمة المخزون: {{ formatCurrency(totalStockValue) }}</strong>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'StockReport',
  data() {
    return {
      loading: false,
      warehouses: [],
      itemGroups: [],
      stockData: [],
      filters: {
        warehouse: '',
        item_group: '',
        stock_status: '',
        search_item: ''
      }
    }
  },
  computed: {
    filteredStockData() {
      let filtered = this.stockData

      // فلتر المخزن
      if (this.filters.warehouse) {
        filtered = filtered.filter(item => item.warehouse === this.filters.warehouse)
      }

      // فلتر مجموعة الأصناف
      if (this.filters.item_group) {
        filtered = filtered.filter(item => item.item_group === this.filters.item_group)
      }

      // فلتر حالة المخزون
      if (this.filters.stock_status) {
        filtered = filtered.filter(item => {
          const status = this.getStockStatusKey(item.actual_qty, item.reorder_level)
          return status === this.filters.stock_status
        })
      }

      // فلتر البحث
      if (this.filters.search_item) {
        const search = this.filters.search_item.toLowerCase()
        filtered = filtered.filter(item => 
          item.item_code.toLowerCase().includes(search) ||
          item.item_name.toLowerCase().includes(search)
        )
      }

      return filtered
    },

    totalItems() {
      return this.stockData.length
    },

    inStockItems() {
      return this.stockData.filter(item => item.actual_qty > (item.reorder_level || 0)).length
    },

    lowStockItems() {
      return this.stockData.filter(item => {
        const qty = item.actual_qty || 0
        const reorder = item.reorder_level || 0
        return qty > 0 && qty <= reorder && reorder > 0
      }).length
    },

    outOfStockItems() {
      return this.stockData.filter(item => (item.actual_qty || 0) <= 0).length
    },

    totalStockValue() {
      return this.filteredStockData.reduce((total, item) => total + (item.stock_value || 0), 0)
    }
  },
  methods: {
    async fetchWarehouses() {
      try {
        const res = await this.$frappe.get_list('Warehouse', 'fields=["name", "warehouse_name"]&filters=[["disabled", "=", 0]]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.warehouses = res.data
        }
      } catch (error) {
        console.error('Error fetching warehouses:', error)
      }
    },

    async fetchItemGroups() {
      try {
        const res = await this.$frappe.get_list('Item Group', 'fields=["name", "item_group_name"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.itemGroups = res.data
        }
      } catch (error) {
        console.error('Error fetching item groups:', error)
      }
    },

    async generateReport() {
      this.loading = true
      try {
        // جلب بيانات المخزون من جدول Bin
        const binRes = await this.$frappe.get_list(
          'Bin',
          'fields=["item_code", "warehouse", "actual_qty", "valuation_rate", "stock_value"]&limit_page_length=5000'
        )

        if (binRes.status_code === 200) {
          this.stockData = []
          
          for (let bin of binRes.data) {
            // جلب تفاصيل الصنف
            const itemRes = await this.$frappe.get_doc('Item', bin.item_code)
            if (itemRes.status_code === 200) {
              const item = itemRes.data
              
              // جلب تفاصيل المخزن
              const warehouseRes = await this.$frappe.get_doc('Warehouse', bin.warehouse)
              const warehouse = warehouseRes.status_code === 200 ? warehouseRes.data : {}

              this.stockData.push({
                item_code: bin.item_code,
                item_name: item.item_name,
                item_group: item.item_group,
                warehouse: bin.warehouse,
                warehouse_name: warehouse.warehouse_name || bin.warehouse,
                actual_qty: bin.actual_qty || 0,
                stock_uom: item.stock_uom,
                valuation_rate: bin.valuation_rate || 0,
                stock_value: bin.stock_value || 0,
                reorder_level: item.reorder_level || 0
              })
            }
          }
        }
      } catch (error) {
        console.error('Error generating report:', error)
        this.$popIt.error('خطأ', 'فشل في تحميل تقرير المخزون')
      } finally {
        this.loading = false
      }
    },

    getStockClass(qty, reorderLevel) {
      if (qty <= 0) return 'text-danger font-weight-bold'
      if (reorderLevel > 0 && qty <= reorderLevel) return 'text-warning font-weight-bold'
      return 'text-success font-weight-bold'
    },

    getStockStatus(qty, reorderLevel) {
      if (qty <= 0) return 'نفد'
      if (reorderLevel > 0 && qty <= reorderLevel) return 'منخفض'
      return 'متوفر'
    },

    getStockStatusKey(qty, reorderLevel) {
      if (qty <= 0) return 'out_of_stock'
      if (reorderLevel > 0 && qty <= reorderLevel) return 'low_stock'
      return 'in_stock'
    },

    getStatusBadgeClass(qty, reorderLevel) {
      if (qty <= 0) return 'badge badge-danger'
      if (reorderLevel > 0 && qty <= reorderLevel) return 'badge badge-warning'
      return 'badge badge-success'
    },

    formatCurrency(amount) {
      if (!amount) return '0.00 ر.س'
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: 'SAR' 
      }).format(amount)
    },

    exportToExcel() {
      // تصدير البيانات إلى Excel
      this.$popIt.success('قريباً', 'ميزة التصدير ستكون متاحة قريباً')
    },

    printReport() {
      window.print()
    }
  },

  mounted() {
    this.fetchWarehouses()
    this.fetchItemGroups()
    
    // إذا كان هناك مخزن محدد في الرابط
    const warehouseParam = this.$route.params.warehouse
    if (warehouseParam) {
      this.filters.warehouse = warehouseParam
    }
    
    // تشغيل التقرير تلقائياً
    this.generateReport()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: right;
}

.small-box {
  border-radius: 0.25rem;
}

.small-box .inner {
  padding: 10px 15px;
}

.small-box .icon {
  position: absolute;
  top: auto;
  bottom: 5px;
  right: 10px;
  z-index: 0;
  font-size: 70px;
  color: rgba(0,0,0,0.15);
}

@media print {
  .card-tools, .breadcrumb, .content-header {
    display: none !important;
  }
}
</style>
