<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>حركة مخزون</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
              <li class="breadcrumb-item"><router-link to="/warehouses">المخازن</router-link></li>
              <li class="breadcrumb-item active">حركة مخزون</li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-primary text-white">
                <h3 class="card-title">
                  <i class="fas fa-exchange-alt"></i> بيانات حركة المخزون
                </h3>
              </div>
              
              <form @submit.prevent="createStockEntry">
                <div class="card-body">
                  <!-- معلومات أساسية -->
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="stock_entry_type">نوع الحركة *</label>
                        <select v-model="stockEntry.stock_entry_type" @change="onTypeChange" class="form-control" id="stock_entry_type" required>
                          <option value="">اختر نوع الحركة</option>
                          <option value="Material Receipt">استلام مواد</option>
                          <option value="Material Issue">صرف مواد</option>
                          <option value="Material Transfer">نقل مواد</option>
                          <option value="Manufacture">تصنيع</option>
                          <option value="Repack">إعادة تعبئة</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="posting_date">تاريخ الحركة *</label>
                        <input v-model="stockEntry.posting_date" type="date" class="form-control" id="posting_date" required>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="posting_time">وقت الحركة</label>
                        <input v-model="stockEntry.posting_time" type="time" class="form-control" id="posting_time">
                      </div>
                    </div>
                  </div>

                  <!-- المخازن -->
                  <div class="row" v-if="showWarehouses">
                    <div class="col-md-6" v-if="showSourceWarehouse">
                      <div class="form-group">
                        <label for="from_warehouse">من مخزن</label>
                        <select v-model="stockEntry.from_warehouse" class="form-control" id="from_warehouse">
                          <option value="">اختر المخزن المصدر</option>
                          <option v-for="warehouse in warehouses" :key="warehouse.name" :value="warehouse.name">
                            {{ warehouse.warehouse_name }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-6" v-if="showTargetWarehouse">
                      <div class="form-group">
                        <label for="to_warehouse">إلى مخزن</label>
                        <select v-model="stockEntry.to_warehouse" class="form-control" id="to_warehouse">
                          <option value="">اختر المخزن المستهدف</option>
                          <option v-for="warehouse in warehouses" :key="warehouse.name" :value="warehouse.name">
                            {{ warehouse.warehouse_name }}
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- الأصناف -->
                  <div class="row">
                    <div class="col-12">
                      <h4>الأصناف</h4>
                      <div class="table-responsive">
                        <table class="table table-bordered">
                          <thead>
                            <tr>
                              <th>الصنف</th>
                              <th>الكمية</th>
                              <th>وحدة القياس</th>
                              <th>السعر الأساسي</th>
                              <th>القيمة</th>
                              <th v-if="showSourceWarehouse">من مخزن</th>
                              <th v-if="showTargetWarehouse">إلى مخزن</th>
                              <th>إجراءات</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(item, index) in stockEntry.items" :key="index">
                              <td>
                                <select v-model="item.item_code" @change="updateItemDetails(index)" class="form-control">
                                  <option value="">اختر الصنف</option>
                                  <option v-for="product in items" :key="product.name" :value="product.item_code">
                                    {{ product.item_name }}
                                  </option>
                                </select>
                              </td>
                              <td>
                                <input v-model.number="item.qty" @input="calculateValue(index)" type="number" class="form-control" min="0" step="0.01">
                              </td>
                              <td>
                                <input v-model="item.uom" type="text" class="form-control" readonly>
                              </td>
                              <td>
                                <input v-model.number="item.basic_rate" @input="calculateValue(index)" type="number" class="form-control" min="0" step="0.01">
                              </td>
                              <td>
                                <input v-model.number="item.basic_amount" type="number" class="form-control" readonly>
                              </td>
                              <td v-if="showSourceWarehouse">
                                <select v-model="item.s_warehouse" class="form-control">
                                  <option value="">اختر المخزن</option>
                                  <option v-for="warehouse in warehouses" :key="warehouse.name" :value="warehouse.name">
                                    {{ warehouse.warehouse_name }}
                                  </option>
                                </select>
                              </td>
                              <td v-if="showTargetWarehouse">
                                <select v-model="item.t_warehouse" class="form-control">
                                  <option value="">اختر المخزن</option>
                                  <option v-for="warehouse in warehouses" :key="warehouse.name" :value="warehouse.name">
                                    {{ warehouse.warehouse_name }}
                                  </option>
                                </select>
                              </td>
                              <td>
                                <button @click="removeItem(index)" type="button" class="btn btn-danger btn-sm">
                                  <i class="fas fa-trash"></i>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      
                      <button @click="addItem" type="button" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة صنف
                      </button>
                    </div>
                  </div>

                  <!-- المجاميع -->
                  <div class="row mt-3">
                    <div class="col-md-6 offset-md-6">
                      <table class="table">
                        <tr>
                          <th>إجمالي الكمية:</th>
                          <td>{{ totalQty }}</td>
                        </tr>
                        <tr>
                          <th>إجمالي القيمة:</th>
                          <td><strong>{{ formatCurrency(totalValue) }}</strong></td>
                        </tr>
                      </table>
                    </div>
                  </div>

                  <!-- ملاحظات -->
                  <div class="form-group">
                    <label for="remarks">ملاحظات</label>
                    <textarea v-model="stockEntry.remarks" class="form-control" id="remarks" rows="3"></textarea>
                  </div>
                </div>

                <div class="card-footer">
                  <button type="submit" class="btn btn-primary" :disabled="loading">
                    <i class="fas fa-save"></i> 
                    {{ loading ? 'جاري الحفظ...' : 'حفظ الحركة' }}
                  </button>
                  <router-link to="/warehouses" class="btn btn-secondary ml-2">
                    <i class="fas fa-times"></i> إلغاء
                  </router-link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'StockEntry',
  data() {
    return {
      loading: false,
      warehouses: [],
      items: [],
      stockEntry: {
        stock_entry_type: '',
        posting_date: new Date().toISOString().split('T')[0],
        posting_time: new Date().toTimeString().split(' ')[0].substring(0, 5),
        from_warehouse: '',
        to_warehouse: '',
        remarks: '',
        items: [
          {
            item_code: '',
            item_name: '',
            qty: 0,
            uom: '',
            basic_rate: 0,
            basic_amount: 0,
            s_warehouse: '',
            t_warehouse: ''
          }
        ]
      }
    }
  },
  computed: {
    showWarehouses() {
      return ['Material Transfer'].includes(this.stockEntry.stock_entry_type)
    },
    
    showSourceWarehouse() {
      return ['Material Issue', 'Material Transfer'].includes(this.stockEntry.stock_entry_type)
    },
    
    showTargetWarehouse() {
      return ['Material Receipt', 'Material Transfer'].includes(this.stockEntry.stock_entry_type)
    },
    
    totalQty() {
      return this.stockEntry.items.reduce((sum, item) => sum + (item.qty || 0), 0)
    },
    
    totalValue() {
      return this.stockEntry.items.reduce((sum, item) => sum + (item.basic_amount || 0), 0)
    }
  },
  methods: {
    async fetchWarehouses() {
      try {
        const res = await this.$frappe.get_list('Warehouse', 'fields=["name", "warehouse_name"]&filters=[["disabled", "=", 0]]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.warehouses = res.data
          
          // إذا كان هناك مخزن محدد في الرابط
          const warehouseParam = this.$route.query.warehouse
          if (warehouseParam) {
            this.stockEntry.to_warehouse = warehouseParam
          }
        }
      } catch (error) {
        console.error('Error fetching warehouses:', error)
      }
    },

    async fetchItems() {
      try {
        const res = await this.$frappe.get_list('Item', 'fields=["name", "item_code", "item_name", "stock_uom", "standard_rate"]&filters=[["is_stock_item", "=", 1]]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.items = res.data
        }
      } catch (error) {
        console.error('Error fetching items:', error)
      }
    },

    onTypeChange() {
      // إعادة تعيين المخازن عند تغيير نوع الحركة
      this.stockEntry.from_warehouse = ''
      this.stockEntry.to_warehouse = ''
      
      // تحديث مخازن الأصناف
      this.stockEntry.items.forEach(item => {
        item.s_warehouse = ''
        item.t_warehouse = ''
      })
    },

    updateItemDetails(index) {
      const selectedItem = this.items.find(item => item.item_code === this.stockEntry.items[index].item_code)
      if (selectedItem) {
        this.stockEntry.items[index].item_name = selectedItem.item_name
        this.stockEntry.items[index].uom = selectedItem.stock_uom
        this.stockEntry.items[index].basic_rate = selectedItem.standard_rate || 0
        this.calculateValue(index)
        
        // تعيين المخازن الافتراضية
        if (this.stockEntry.from_warehouse) {
          this.stockEntry.items[index].s_warehouse = this.stockEntry.from_warehouse
        }
        if (this.stockEntry.to_warehouse) {
          this.stockEntry.items[index].t_warehouse = this.stockEntry.to_warehouse
        }
      }
    },

    calculateValue(index) {
      const item = this.stockEntry.items[index]
      item.basic_amount = (item.qty || 0) * (item.basic_rate || 0)
    },

    addItem() {
      this.stockEntry.items.push({
        item_code: '',
        item_name: '',
        qty: 0,
        uom: '',
        basic_rate: 0,
        basic_amount: 0,
        s_warehouse: this.stockEntry.from_warehouse || '',
        t_warehouse: this.stockEntry.to_warehouse || ''
      })
    },

    removeItem(index) {
      if (this.stockEntry.items.length > 1) {
        this.stockEntry.items.splice(index, 1)
      }
    },

    async createStockEntry() {
      // التحقق من صحة البيانات
      if (!this.stockEntry.stock_entry_type) {
        this.$popIt.error('خطأ', 'يرجى اختيار نوع الحركة')
        return
      }

      const validItems = this.stockEntry.items.filter(item => item.item_code && item.qty > 0)
      if (validItems.length === 0) {
        this.$popIt.error('خطأ', 'يرجى إضافة صنف واحد على الأقل بكمية أكبر من صفر')
        return
      }

      this.loading = true
      try {
        const stockEntryData = {
          doctype: 'Stock Entry',
          stock_entry_type: this.stockEntry.stock_entry_type,
          posting_date: this.stockEntry.posting_date,
          posting_time: this.stockEntry.posting_time,
          from_warehouse: this.stockEntry.from_warehouse || undefined,
          to_warehouse: this.stockEntry.to_warehouse || undefined,
          remarks: this.stockEntry.remarks,
          items: validItems.map(item => ({
            item_code: item.item_code,
            qty: item.qty,
            uom: item.uom,
            basic_rate: item.basic_rate,
            basic_amount: item.basic_amount,
            s_warehouse: item.s_warehouse || this.stockEntry.from_warehouse || undefined,
            t_warehouse: item.t_warehouse || this.stockEntry.to_warehouse || undefined
          }))
        }

        const res = await this.$frappe.new_doc('Stock Entry', stockEntryData)
        
        if (res.status_code === 200) {
          this.$popIt.success('نجح', `تم إنشاء حركة المخزون بنجاح: ${res.data.name}`)
          this.$router.push('/warehouses')
        } else {
          this.$popIt.error('خطأ', res.text || 'فشل في إنشاء حركة المخزون')
        }
      } catch (error) {
        console.error('Error creating stock entry:', error)
        const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ أثناء إنشاء حركة المخزون'
        this.$popIt.error('خطأ', errorMessage)
      } finally {
        this.loading = false
      }
    },

    formatCurrency(amount) {
      if (!amount) return '0.00 ر.س'
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: 'SAR' 
      }).format(amount)
    }
  },

  mounted() {
    this.fetchWarehouses()
    this.fetchItems()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: center;
}

.form-control {
  text-align: right;
}
</style>
