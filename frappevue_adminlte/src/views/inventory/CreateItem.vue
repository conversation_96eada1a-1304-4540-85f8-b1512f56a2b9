<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>إضافة صنف جديد</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
              <li class="breadcrumb-item"><router-link to="/items">الأصناف</router-link></li>
              <li class="breadcrumb-item active">إضافة صنف جديد</li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-primary text-white">
                <h3 class="card-title">
                  <i class="fas fa-box"></i> بيانات الصنف
                </h3>
              </div>
              
              <form @submit.prevent="createItem">
                <div class="card-body">
                  <!-- المعلومات الأساسية -->
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="item_code">كود الصنف *</label>
                        <input v-model="item.item_code" type="text" class="form-control" id="item_code" required>
                        <small class="form-text text-muted">كود فريد للصنف</small>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="item_name">اسم الصنف *</label>
                        <input v-model="item.item_name" type="text" class="form-control" id="item_name" required>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="item_group">مجموعة الصنف *</label>
                        <select v-model="item.item_group" class="form-control" id="item_group" required>
                          <option value="">اختر المجموعة</option>
                          <option v-for="group in itemGroups" :key="group.name" :value="group.name">
                            {{ group.item_group_name }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="stock_uom">وحدة القياس *</label>
                        <select v-model="item.stock_uom" class="form-control" id="stock_uom" required>
                          <option value="">اختر وحدة القياس</option>
                          <option v-for="uom in uoms" :key="uom.name" :value="uom.name">
                            {{ uom.uom_name }}
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- التسعير -->
                  <h4>معلومات التسعير</h4>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="standard_rate">السعر القياسي</label>
                        <input v-model.number="item.standard_rate" type="number" class="form-control" id="standard_rate" step="0.01" min="0">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="valuation_rate">سعر التقييم</label>
                        <input v-model.number="item.valuation_rate" type="number" class="form-control" id="valuation_rate" step="0.01" min="0">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="last_purchase_rate">آخر سعر شراء</label>
                        <input v-model.number="item.last_purchase_rate" type="number" class="form-control" id="last_purchase_rate" step="0.01" min="0">
                      </div>
                    </div>
                  </div>

                  <!-- إعدادات المخزون -->
                  <h4>إعدادات المخزون</h4>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <div class="form-check">
                          <input v-model="item.maintain_stock" type="checkbox" class="form-check-input" id="maintain_stock">
                          <label class="form-check-label" for="maintain_stock">
                            إدارة المخزون
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <div class="form-check">
                          <input v-model="item.is_stock_item" type="checkbox" class="form-check-input" id="is_stock_item">
                          <label class="form-check-label" for="is_stock_item">
                            صنف مخزني
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <div class="form-check">
                          <input v-model="item.has_serial_no" type="checkbox" class="form-check-input" id="has_serial_no">
                          <label class="form-check-label" for="has_serial_no">
                            له رقم تسلسلي
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- مستويات المخزون -->
                  <div class="row" v-if="item.maintain_stock">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="reorder_level">مستوى إعادة الطلب</label>
                        <input v-model.number="item.reorder_level" type="number" class="form-control" id="reorder_level" min="0">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="min_order_qty">الحد الأدنى للطلب</label>
                        <input v-model.number="item.min_order_qty" type="number" class="form-control" id="min_order_qty" min="0">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="max_order_qty">الحد الأقصى للطلب</label>
                        <input v-model.number="item.max_order_qty" type="number" class="form-control" id="max_order_qty" min="0">
                      </div>
                    </div>
                  </div>

                  <!-- الوصف -->
                  <div class="form-group">
                    <label for="description">الوصف</label>
                    <textarea v-model="item.description" class="form-control" id="description" rows="3"></textarea>
                  </div>

                  <!-- إعدادات البيع والشراء -->
                  <div class="row">
                    <div class="col-md-6">
                      <h5>إعدادات البيع</h5>
                      <div class="form-check">
                        <input v-model="item.is_sales_item" type="checkbox" class="form-check-input" id="is_sales_item">
                        <label class="form-check-label" for="is_sales_item">
                          متاح للبيع
                        </label>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <h5>إعدادات الشراء</h5>
                      <div class="form-check">
                        <input v-model="item.is_purchase_item" type="checkbox" class="form-check-input" id="is_purchase_item">
                        <label class="form-check-label" for="is_purchase_item">
                          متاح للشراء
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-footer">
                  <button type="submit" class="btn btn-primary" :disabled="loading">
                    <i class="fas fa-save"></i> 
                    {{ loading ? 'جاري الحفظ...' : 'حفظ الصنف' }}
                  </button>
                  <router-link to="/items" class="btn btn-secondary ml-2">
                    <i class="fas fa-times"></i> إلغاء
                  </router-link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'CreateItem',
  data() {
    return {
      loading: false,
      itemGroups: [],
      uoms: [],
      item: {
        item_code: '',
        item_name: '',
        item_group: '',
        stock_uom: '',
        standard_rate: 0,
        valuation_rate: 0,
        last_purchase_rate: 0,
        maintain_stock: true,
        is_stock_item: true,
        has_serial_no: false,
        reorder_level: 0,
        min_order_qty: 1,
        max_order_qty: 0,
        description: '',
        is_sales_item: true,
        is_purchase_item: true
      }
    }
  },
  methods: {
    async fetchItemGroups() {
      try {
        const res = await this.$frappe.get_list('Item Group', 'fields=["name", "item_group_name"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.itemGroups = res.data
        }
      } catch (error) {
        console.error('Error fetching item groups:', error)
      }
    },

    async fetchUOMs() {
      try {
        const res = await this.$frappe.get_list('UOM', 'fields=["name", "uom_name"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.uoms = res.data
        }
      } catch (error) {
        console.error('Error fetching UOMs:', error)
      }
    },

    async createItem() {
      // التحقق من صحة البيانات
      if (!this.item.item_code || !this.item.item_name || !this.item.item_group || !this.item.stock_uom) {
        this.$popIt.error('خطأ', 'يرجى ملء جميع الحقول المطلوبة')
        return
      }

      this.loading = true
      try {
        const itemData = {
          doctype: 'Item',
          item_code: this.item.item_code,
          item_name: this.item.item_name,
          item_group: this.item.item_group,
          stock_uom: this.item.stock_uom,
          standard_rate: this.item.standard_rate,
          valuation_rate: this.item.valuation_rate,
          last_purchase_rate: this.item.last_purchase_rate,
          maintain_stock: this.item.maintain_stock ? 1 : 0,
          is_stock_item: this.item.is_stock_item ? 1 : 0,
          has_serial_no: this.item.has_serial_no ? 1 : 0,
          reorder_level: this.item.reorder_level,
          min_order_qty: this.item.min_order_qty,
          max_order_qty: this.item.max_order_qty,
          description: this.item.description,
          is_sales_item: this.item.is_sales_item ? 1 : 0,
          is_purchase_item: this.item.is_purchase_item ? 1 : 0
        }

        const res = await this.$frappe.new_doc('Item', itemData)
        
        if (res.status_code === 200) {
          this.$popIt.success('نجح', `تم إنشاء الصنف بنجاح: ${res.data.name}`)
          this.$router.push('/items')
        } else {
          this.$popIt.error('خطأ', res.text || 'فشل في إنشاء الصنف')
        }
      } catch (error) {
        console.error('Error creating item:', error)
        const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ أثناء إنشاء الصنف'
        this.$popIt.error('خطأ', errorMessage)
      } finally {
        this.loading = false
      }
    }
  },

  mounted() {
    this.fetchItemGroups()
    this.fetchUOMs()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.form-control {
  text-align: right;
}

.form-check-label {
  margin-right: 1.25rem;
}
</style>
