<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>إدارة الأصناف</h1>
          </div>
          <div class="col-sm-6">
            <div class="float-sm-right">
              <router-link to="/create-item" class="btn btn-primary mb-2">
                <i class="fas fa-plus"></i> إضافة صنف جديد
              </router-link>
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
                <li class="breadcrumb-item active">الأصناف</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">قائمة الأصناف</h3>
                <div class="card-tools">
                  <div class="input-group input-group-sm" style="width: 250px;">
                    <input v-model="searchQuery" type="text" class="form-control float-right" placeholder="البحث في الأصناف...">
                    <div class="input-group-append">
                      <button @click="searchItems" class="btn btn-default">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>كود الصنف</th>
                      <th>اسم الصنف</th>
                      <th>المجموعة</th>
                      <th>وحدة القياس</th>
                      <th>السعر القياسي</th>
                      <th>الكمية المتاحة</th>
                      <th>الحالة</th>
                      <th>إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in filteredItems" :key="item.name">
                      <td>{{ item.item_code }}</td>
                      <td>
                        <router-link :to="`/item/${item.name}`" class="text-primary">
                          {{ item.item_name }}
                        </router-link>
                      </td>
                      <td>{{ item.item_group }}</td>
                      <td>{{ item.stock_uom }}</td>
                      <td>{{ formatCurrency(item.standard_rate) }}</td>
                      <td>
                        <span :class="getStockClass(item.actual_qty)">
                          {{ item.actual_qty || 0 }}
                        </span>
                      </td>
                      <td>
                        <span :class="item.disabled ? 'badge badge-danger' : 'badge badge-success'">
                          {{ item.disabled ? 'معطل' : 'نشط' }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group">
                          <router-link :to="`/item/${item.name}`" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link :to="`/edit-item/${item.name}`" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button @click="toggleItemStatus(item)" class="btn btn-sm" 
                                  :class="item.disabled ? 'btn-success' : 'btn-danger'">
                            <i :class="item.disabled ? 'fas fa-check' : 'fas fa-ban'"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="card-footer">
                <div class="row">
                  <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                      عرض {{ items.length }} من الأصناف
                    </div>
                  </div>
                  <div class="col-sm-12 col-md-7">
                    <button @click="loadMoreItems" v-if="hasMore" class="btn btn-outline-primary">
                      <i class="fas fa-plus"></i> تحميل المزيد
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'ItemList',
  data() {
    return {
      items: [],
      searchQuery: '',
      loading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20
    }
  },
  computed: {
    filteredItems() {
      if (!this.searchQuery) return this.items
      
      const query = this.searchQuery.toLowerCase()
      return this.items.filter(item => 
        item.item_code.toLowerCase().includes(query) ||
        item.item_name.toLowerCase().includes(query) ||
        item.item_group.toLowerCase().includes(query)
      )
    }
  },
  methods: {
    async fetchItems() {
      this.loading = true
      try {
        const fields = [
          'name', 'item_code', 'item_name', 'item_group', 
          'stock_uom', 'standard_rate', 'disabled'
        ]
        
        const res = await this.$frappe.get_list(
          'Item', 
          `fields=["${fields.join('", "')}"]&limit_page_length=${this.pageSize}&limit_start=${(this.currentPage - 1) * this.pageSize}`
        )
        
        if (res.status_code === 200) {
          // جلب الكميات المتاحة لكل صنف
          for (let item of res.data) {
            await this.fetchItemStock(item)
          }
          
          if (this.currentPage === 1) {
            this.items = res.data
          } else {
            this.items.push(...res.data)
          }
          
          this.hasMore = res.data.length === this.pageSize
        }
      } catch (error) {
        console.error('Error fetching items:', error)
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة الأصناف')
      } finally {
        this.loading = false
      }
    },

    async fetchItemStock(item) {
      try {
        const stockRes = await this.$frappe.get_list(
          'Bin',
          `filters=[["item_code", "=", "${item.item_code}"]]&fields=["actual_qty"]`
        )
        
        if (stockRes.status_code === 200 && stockRes.data.length > 0) {
          item.actual_qty = stockRes.data.reduce((total, bin) => total + (bin.actual_qty || 0), 0)
        } else {
          item.actual_qty = 0
        }
      } catch (error) {
        item.actual_qty = 0
      }
    },

    async searchItems() {
      this.currentPage = 1
      this.hasMore = true
      await this.fetchItems()
    },

    async loadMoreItems() {
      this.currentPage++
      await this.fetchItems()
    },

    async toggleItemStatus(item) {
      try {
        const newStatus = !item.disabled
        const res = await this.$frappe.update_doc('Item', item.name, {
          disabled: newStatus
        })
        
        if (res.status_code === 200) {
          item.disabled = newStatus
          this.$popIt.success('نجح', `تم ${newStatus ? 'تعطيل' : 'تفعيل'} الصنف بنجاح`)
        }
      } catch (error) {
        this.$popIt.error('خطأ', 'فشل في تحديث حالة الصنف')
      }
    },

    getStockClass(qty) {
      if (qty <= 0) return 'text-danger font-weight-bold'
      if (qty <= 10) return 'text-warning font-weight-bold'
      return 'text-success font-weight-bold'
    },

    formatCurrency(amount) {
      if (!amount) return '0.00 ر.س'
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: 'SAR' 
      }).format(amount)
    }
  },

  mounted() {
    this.fetchItems()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: right;
}

.btn-group .btn {
  margin-left: 2px;
}

.card-tools {
  direction: ltr;
}

.input-group input {
  text-align: right;
}
</style>
