<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>إدارة المخازن</h1>
          </div>
          <div class="col-sm-6">
            <div class="float-sm-right">
              <router-link to="/create-warehouse" class="btn btn-primary mb-2">
                <i class="fas fa-plus"></i> إضافة مخزن جديد
              </router-link>
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
                <li class="breadcrumb-item active">المخازن</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">قائمة المخازن</h3>
                <div class="card-tools">
                  <div class="input-group input-group-sm" style="width: 250px;">
                    <input v-model="searchQuery" type="text" class="form-control float-right" placeholder="البحث في المخازن...">
                    <div class="input-group-append">
                      <button @click="searchWarehouses" class="btn btn-default">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                  <thead>
                    <tr>
                      <th>اسم المخزن</th>
                      <th>المخزن الرئيسي</th>
                      <th>الشركة</th>
                      <th>عدد الأصناف</th>
                      <th>القيمة الإجمالية</th>
                      <th>الحالة</th>
                      <th>إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="warehouse in filteredWarehouses" :key="warehouse.name">
                      <td>
                        <router-link :to="`/warehouse/${warehouse.name}`" class="text-primary">
                          <i class="fas fa-warehouse"></i> {{ warehouse.warehouse_name }}
                        </router-link>
                      </td>
                      <td>{{ warehouse.parent_warehouse || '-' }}</td>
                      <td>{{ warehouse.company }}</td>
                      <td>
                        <span class="badge badge-info">{{ warehouse.item_count || 0 }}</span>
                      </td>
                      <td>{{ formatCurrency(warehouse.total_value) }}</td>
                      <td>
                        <span :class="warehouse.disabled ? 'badge badge-danger' : 'badge badge-success'">
                          {{ warehouse.disabled ? 'معطل' : 'نشط' }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group">
                          <router-link :to="`/warehouse/${warehouse.name}`" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link :to="`/stock-entry?warehouse=${warehouse.name}`" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> إدخال
                          </router-link>
                          <button @click="viewStockReport(warehouse)" class="btn btn-sm btn-warning">
                            <i class="fas fa-chart-bar"></i> تقرير
                          </button>
                          <button @click="toggleWarehouseStatus(warehouse)" class="btn btn-sm" 
                                  :class="warehouse.disabled ? 'btn-success' : 'btn-danger'">
                            <i :class="warehouse.disabled ? 'fas fa-check' : 'fas fa-ban'"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="card-footer">
                <div class="row">
                  <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                      عرض {{ warehouses.length }} من المخازن
                    </div>
                  </div>
                  <div class="col-sm-12 col-md-7">
                    <button @click="refreshData" class="btn btn-outline-primary">
                      <i class="fas fa-sync"></i> تحديث البيانات
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
              <div class="inner">
                <h3>{{ totalWarehouses }}</h3>
                <p>إجمالي المخازن</p>
              </div>
              <div class="icon">
                <i class="fas fa-warehouse"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
              <div class="inner">
                <h3>{{ activeWarehouses }}</h3>
                <p>المخازن النشطة</p>
              </div>
              <div class="icon">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
              <div class="inner">
                <h3>{{ formatCurrency(totalStockValue) }}</h3>
                <p>إجمالي قيمة المخزون</p>
              </div>
              <div class="icon">
                <i class="fas fa-dollar-sign"></i>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
              <div class="inner">
                <h3>{{ lowStockItems }}</h3>
                <p>أصناف منخفضة المخزون</p>
              </div>
              <div class="icon">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'WarehouseList',
  data() {
    return {
      warehouses: [],
      searchQuery: '',
      loading: false,
      totalStockValue: 0,
      lowStockItems: 0
    }
  },
  computed: {
    filteredWarehouses() {
      if (!this.searchQuery) return this.warehouses
      
      const query = this.searchQuery.toLowerCase()
      return this.warehouses.filter(warehouse => 
        warehouse.warehouse_name.toLowerCase().includes(query) ||
        warehouse.company.toLowerCase().includes(query)
      )
    },
    
    totalWarehouses() {
      return this.warehouses.length
    },
    
    activeWarehouses() {
      return this.warehouses.filter(w => !w.disabled).length
    }
  },
  methods: {
    async fetchWarehouses() {
      this.loading = true
      try {
        const fields = [
          'name', 'warehouse_name', 'parent_warehouse', 
          'company', 'disabled'
        ]
        
        const res = await this.$frappe.get_list(
          'Warehouse', 
          `fields=["${fields.join('", "')}"]&limit_page_length=1000`
        )
        
        if (res.status_code === 200) {
          this.warehouses = res.data
          
          // جلب إحصائيات كل مخزن
          for (let warehouse of this.warehouses) {
            await this.fetchWarehouseStats(warehouse)
          }
        }
      } catch (error) {
        console.error('Error fetching warehouses:', error)
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة المخازن')
      } finally {
        this.loading = false
      }
    },

    async fetchWarehouseStats(warehouse) {
      try {
        // جلب عدد الأصناف في المخزن
        const binRes = await this.$frappe.get_list(
          'Bin',
          `filters=[["warehouse", "=", "${warehouse.name}"], ["actual_qty", ">", 0]]&fields=["item_code", "actual_qty", "valuation_rate"]`
        )
        
        if (binRes.status_code === 200) {
          warehouse.item_count = binRes.data.length
          warehouse.total_value = binRes.data.reduce((total, bin) => {
            return total + (bin.actual_qty * (bin.valuation_rate || 0))
          }, 0)
          
          this.totalStockValue += warehouse.total_value
        } else {
          warehouse.item_count = 0
          warehouse.total_value = 0
        }
      } catch (error) {
        warehouse.item_count = 0
        warehouse.total_value = 0
      }
    },

    async fetchLowStockItems() {
      try {
        const res = await this.$frappe.get_list(
          'Item',
          'filters=[["maintain_stock", "=", 1]]&fields=["name", "reorder_level"]'
        )
        
        if (res.status_code === 200) {
          let lowStockCount = 0
          
          for (let item of res.data) {
            if (item.reorder_level > 0) {
              const stockRes = await this.$frappe.get_list(
                'Bin',
                `filters=[["item_code", "=", "${item.name}"]]&fields=["actual_qty"]`
              )
              
              if (stockRes.status_code === 200) {
                const totalQty = stockRes.data.reduce((total, bin) => total + (bin.actual_qty || 0), 0)
                if (totalQty <= item.reorder_level) {
                  lowStockCount++
                }
              }
            }
          }
          
          this.lowStockItems = lowStockCount
        }
      } catch (error) {
        console.error('Error fetching low stock items:', error)
      }
    },

    async searchWarehouses() {
      // البحث يتم محلياً في computed property
    },

    async refreshData() {
      this.totalStockValue = 0
      await this.fetchWarehouses()
      await this.fetchLowStockItems()
    },

    async toggleWarehouseStatus(warehouse) {
      try {
        const newStatus = !warehouse.disabled
        const res = await this.$frappe.update_doc('Warehouse', warehouse.name, {
          disabled: newStatus
        })
        
        if (res.status_code === 200) {
          warehouse.disabled = newStatus
          this.$popIt.success('نجح', `تم ${newStatus ? 'تعطيل' : 'تفعيل'} المخزن بنجاح`)
        }
      } catch (error) {
        this.$popIt.error('خطأ', 'فشل في تحديث حالة المخزن')
      }
    },

    viewStockReport(warehouse) {
      this.$router.push(`/stock-report/${warehouse.name}`)
    },

    formatCurrency(amount) {
      if (!amount) return '0.00 ر.س'
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: 'SAR' 
      }).format(amount)
    }
  },

  mounted() {
    this.refreshData()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: right;
}

.btn-group .btn {
  margin-left: 2px;
}

.small-box {
  border-radius: 0.25rem;
}

.small-box .inner {
  padding: 10px 15px;
}

.small-box .icon {
  position: absolute;
  top: auto;
  bottom: 5px;
  right: 10px;
  z-index: 0;
  font-size: 70px;
  color: rgba(0,0,0,0.15);
}

.small-box h3 {
  font-size: 2.2rem;
  font-weight: bold;
  margin: 0 0 10px 0;
  white-space: nowrap;
  padding: 0;
}
</style>
