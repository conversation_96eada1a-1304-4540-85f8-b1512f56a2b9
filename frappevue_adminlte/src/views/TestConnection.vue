<template>
  <div class="content-wrapper">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>اختبار الاتصال مع Frappe</h1>
          </div>
        </div>
      </div>
    </section>

    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">حالة الاتصال</h3>
                <div class="card-tools">
                  <button @click="testConnection" class="btn btn-primary" :disabled="testing">
                    <i class="fas fa-sync" :class="{ 'fa-spin': testing }"></i>
                    {{ testing ? 'جاري الاختبار...' : 'اختبار الاتصال' }}
                  </button>
                </div>
              </div>

              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <h5>إعدادات الاتصال:</h5>
                    <ul class="list-group">
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        Domain (appConfig.json)
                        <span class="badge badge-primary">{{ appConfigDomain }}</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        Environment Variable
                        <span class="badge badge-info">{{ envDomain }}</span>
                      </li>
                      <li class="list-group-item d-flex justify-content-between align-items-center">
                        Final URL
                        <span class="badge badge-success">{{ finalUrl }}</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div class="col-md-6">
                    <h5>نتائج الاختبار:</h5>
                    <div v-if="connectionResult">
                      <div class="alert" :class="connectionResult.success ? 'alert-success' : 'alert-danger'">
                        <h6>
                          <i :class="connectionResult.success ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                          {{ connectionResult.success ? 'نجح الاتصال' : 'فشل الاتصال' }}
                        </h6>
                        <p><strong>الحالة:</strong> {{ connectionResult.status }}</p>
                        <p><strong>الرسالة:</strong> {{ connectionResult.message }}</p>
                        <p v-if="connectionResult.error"><strong>الخطأ:</strong> {{ connectionResult.error }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <hr>

                <div class="row">
                  <div class="col-12">
                    <h5>اختبار API:</h5>
                    <div class="btn-group" role="group">
                      <button @click="testAPI('ping')" class="btn btn-outline-primary" :disabled="testing">
                        اختبار Ping
                      </button>
                      <button @click="testAPI('version')" class="btn btn-outline-info" :disabled="testing">
                        اختبار Version
                      </button>
                      <button @click="testAPI('whoami')" class="btn btn-outline-warning" :disabled="testing">
                        اختبار المستخدم
                      </button>
                      <button @click="testAPI('doctype')" class="btn btn-outline-success" :disabled="testing">
                        اختبار DocType
                      </button>
                    </div>
                  </div>
                </div>

                <div v-if="apiResults.length > 0" class="mt-3">
                  <h6>نتائج اختبار API:</h6>
                  <div class="table-responsive">
                    <table class="table table-sm">
                      <thead>
                        <tr>
                          <th>الاختبار</th>
                          <th>الحالة</th>
                          <th>النتيجة</th>
                          <th>الوقت</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="result in apiResults" :key="result.test">
                          <td>{{ result.test }}</td>
                          <td>
                            <span :class="result.success ? 'badge badge-success' : 'badge badge-danger'">
                              {{ result.success ? 'نجح' : 'فشل' }}
                            </span>
                          </td>
                          <td>{{ result.message }}</td>
                          <td>{{ result.timestamp }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'TestConnection',
  data() {
    return {
      testing: false,
      connectionResult: null,
      apiResults: []
    }
  },
  computed: {
    appConfigDomain() {
      return window.APP_CONFIG?.domain || 'غير محدد'
    },
    envDomain() {
      return process.env.VUE_APP_FRAPPE_DOMAIN || 'غير محدد'
    },
    finalUrl() {
      return this.envDomain !== 'غير محدد' ? this.envDomain : this.appConfigDomain
    }
  },
  methods: {
    async testConnection() {
      this.testing = true
      this.connectionResult = null
      
      try {
        const response = await fetch(`${this.finalUrl}/api/method/ping`, {
          method: 'GET',
          credentials: 'include'
        })
        
        if (response.ok) {
          const data = await response.json()
          this.connectionResult = {
            success: true,
            status: response.status,
            message: 'تم الاتصال بنجاح مع خادم Frappe',
            data: data
          }
        } else {
          this.connectionResult = {
            success: false,
            status: response.status,
            message: `فشل الاتصال - كود الخطأ: ${response.status}`,
            error: response.statusText
          }
        }
      } catch (error) {
        this.connectionResult = {
          success: false,
          status: 'خطأ في الشبكة',
          message: 'لا يمكن الوصول إلى خادم Frappe',
          error: error.message
        }
      } finally {
        this.testing = false
      }
    },

    async testAPI(testType) {
      this.testing = true
      
      const timestamp = new Date().toLocaleTimeString('ar-SA')
      let url, testName
      
      switch (testType) {
        case 'ping':
          url = `${this.finalUrl}/api/method/ping`
          testName = 'Ping Test'
          break
        case 'version':
          url = `${this.finalUrl}/api/method/frappe.utils.get_version`
          testName = 'Version Test'
          break
        case 'whoami':
          url = `${this.finalUrl}/api/method/frappe.auth.get_logged_user`
          testName = 'User Test'
          break
        case 'doctype':
          url = `${this.finalUrl}/api/resource/DocType/User`
          testName = 'DocType Test'
          break
      }
      
      try {
        const response = await fetch(url, {
          method: 'GET',
          credentials: 'include'
        })
        
        if (response.ok) {
          const data = await response.json()
          this.apiResults.unshift({
            test: testName,
            success: true,
            message: 'نجح',
            timestamp: timestamp,
            data: data
          })
        } else {
          this.apiResults.unshift({
            test: testName,
            success: false,
            message: `فشل - ${response.status}`,
            timestamp: timestamp
          })
        }
      } catch (error) {
        this.apiResults.unshift({
          test: testName,
          success: false,
          message: `خطأ - ${error.message}`,
          timestamp: timestamp
        })
      } finally {
        this.testing = false
      }
    }
  },

  mounted() {
    // اختبار تلقائي عند تحميل الصفحة
    setTimeout(() => {
      this.testConnection()
    }, 1000)
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: right;
}

.btn-group .btn {
  margin-left: 5px;
}
</style>
