<template>
  <div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>إنشاء فاتورة مبيعات جديدة</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><router-link to="/">الرئيسية</router-link></li>
              <li class="breadcrumb-item"><router-link to="/my-invoices">الفواتير</router-link></li>
              <li class="breadcrumb-item active">إنشاء فاتورة جديدة</li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card invoice-form">
              <div class="card-header bg-primary text-white">
                <h3 class="card-title">
                  <i class="fas fa-file-invoice"></i> بيانات الفاتورة
                </h3>
              </div>
              
              <form @submit.prevent="createInvoice">
                <div class="card-body">
                  <!-- Customer Information -->
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="customer">العميل *</label>
                        <div class="input-group">
                          <select v-model="invoice.customer" class="form-control" id="customer" required>
                            <option value="">اختر العميل</option>
                            <option v-for="customer in customers" :key="customer.name" :value="customer.name">
                              {{ customer.customer_name }}
                            </option>
                          </select>
                          <div class="input-group-append">
                            <router-link to="/create-customer" class="btn btn-outline-secondary">
                              <i class="fas fa-plus"></i> إضافة عميل
                            </router-link>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="posting_date">تاريخ الفاتورة *</label>
                        <input v-model="invoice.posting_date" type="date" class="form-control" id="posting_date" required>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="due_date">تاريخ الاستحقاق</label>
                        <input v-model="invoice.due_date" type="date" class="form-control" id="due_date">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="currency">العملة</label>
                        <select v-model="invoice.currency" class="form-control" id="currency">
                          <option value="SAR">ريال سعودي (SAR)</option>
                          <option value="USD">دولار أمريكي (USD)</option>
                          <option value="EUR">يورو (EUR)</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- Items Section -->
                  <div class="row">
                    <div class="col-12">
                      <h4>الأصناف</h4>
                      <div class="table-responsive">
                        <table class="table table-bordered">
                          <thead>
                            <tr>
                              <th>الصنف</th>
                              <th>الكمية</th>
                              <th>السعر</th>
                              <th>المبلغ</th>
                              <th>إجراءات</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(item, index) in invoice.items" :key="index">
                              <td>
                                <select v-model="item.item_code" @change="updateItemDetails(index)" class="form-control">
                                  <option value="">اختر الصنف</option>
                                  <option v-for="product in items" :key="product.name" :value="product.item_code">
                                    {{ product.item_name }}
                                  </option>
                                </select>
                              </td>
                              <td>
                                <input v-model.number="item.qty" @input="calculateAmount(index)" type="number" class="form-control" min="1" step="0.01">
                              </td>
                              <td>
                                <input v-model.number="item.rate" @input="calculateAmount(index)" type="number" class="form-control" min="0" step="0.01">
                              </td>
                              <td>
                                <input v-model.number="item.amount" type="number" class="form-control" readonly>
                              </td>
                              <td>
                                <button @click="removeItem(index)" type="button" class="btn btn-danger btn-sm">
                                  <i class="fas fa-trash"></i>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      
                      <button @click="addItem" type="button" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة صنف
                      </button>
                    </div>
                  </div>

                  <!-- Totals -->
                  <div class="row mt-3">
                    <div class="col-md-6 offset-md-6">
                      <table class="table">
                        <tr>
                          <th>المجموع الفرعي:</th>
                          <td>{{ formatCurrency(invoice.total) }}</td>
                        </tr>
                        <tr>
                          <th>ضريبة القيمة المضافة (15%):</th>
                          <td>{{ formatCurrency(invoice.total_taxes_and_charges) }}</td>
                        </tr>
                        <tr>
                          <th>المجموع الكلي:</th>
                          <td><strong>{{ formatCurrency(invoice.grand_total) }}</strong></td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>

                <div class="card-footer">
                  <button type="submit" class="btn btn-primary" :disabled="loading">
                    <i class="fas fa-save"></i> 
                    {{ loading ? 'جاري الحفظ...' : 'حفظ الفاتورة' }}
                  </button>
                  <router-link to="/my-invoices" class="btn btn-secondary ml-2">
                    <i class="fas fa-times"></i> إلغاء
                  </router-link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'CreateInvoice',
  data() {
    return {
      loading: false,
      customers: [],
      items: [],
      invoice: {
        customer: '',
        posting_date: new Date().toISOString().split('T')[0],
        due_date: '',
        currency: 'SAR',
        items: [
          {
            item_code: '',
            item_name: '',
            qty: 1,
            rate: 0,
            amount: 0
          }
        ],
        total: 0,
        total_taxes_and_charges: 0,
        grand_total: 0
      }
    }
  },
  methods: {
    async fetchCustomers() {
      try {
        const res = await this.$frappe.get_list('Customer', 'fields=["name", "customer_name"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.customers = res.data
        }
      } catch (error) {
        console.error('Error fetching customers:', error)
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة العملاء')
      }
    },

    async fetchItems() {
      try {
        const res = await this.$frappe.get_list('Item', 'fields=["name", "item_code", "item_name", "standard_rate"]&limit_page_length=1000')
        if (res.status_code === 200) {
          this.items = res.data
        }
      } catch (error) {
        console.error('Error fetching items:', error)
        this.$popIt.error('خطأ', 'فشل في تحميل قائمة الأصناف')
      }
    },

    updateItemDetails(index) {
      const selectedItem = this.items.find(item => item.item_code === this.invoice.items[index].item_code)
      if (selectedItem) {
        this.invoice.items[index].item_name = selectedItem.item_name
        this.invoice.items[index].rate = selectedItem.standard_rate || 0
        this.calculateAmount(index)
      }
    },

    calculateAmount(index) {
      const item = this.invoice.items[index]
      item.amount = item.qty * item.rate
      this.calculateTotals()
    },

    calculateTotals() {
      this.invoice.total = this.invoice.items.reduce((sum, item) => sum + item.amount, 0)
      this.invoice.total_taxes_and_charges = this.invoice.total * 0.15 // 15% VAT
      this.invoice.grand_total = this.invoice.total + this.invoice.total_taxes_and_charges
    },

    addItem() {
      this.invoice.items.push({
        item_code: '',
        item_name: '',
        qty: 1,
        rate: 0,
        amount: 0
      })
    },

    removeItem(index) {
      if (this.invoice.items.length > 1) {
        this.invoice.items.splice(index, 1)
        this.calculateTotals()
      }
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('ar-SA', { 
        style: 'currency', 
        currency: this.invoice.currency 
      }).format(amount)
    },

    async createInvoice() {
      // Validate form
      if (!this.invoice.customer) {
        this.$popIt.error('خطأ', 'يرجى اختيار العميل')
        return
      }

      if (this.invoice.items.filter(item => item.item_code).length === 0) {
        this.$popIt.error('خطأ', 'يرجى إضافة صنف واحد على الأقل')
        return
      }

      this.loading = true
      try {
        // Prepare invoice data for Frappe
        const invoiceData = {
          doctype: 'Sales Invoice',
          customer: this.invoice.customer,
          posting_date: this.invoice.posting_date,
          due_date: this.invoice.due_date || this.invoice.posting_date,
          currency: this.invoice.currency,
          items: this.invoice.items.filter(item => item.item_code).map(item => ({
            item_code: item.item_code,
            item_name: item.item_name,
            qty: item.qty,
            rate: item.rate,
            amount: item.amount
          })),
          total: this.invoice.total,
          total_taxes_and_charges: this.invoice.total_taxes_and_charges,
          grand_total: this.invoice.grand_total
        }

        const res = await this.$frappe.new_doc('Sales Invoice', invoiceData)

        if (res.status_code === 200) {
          this.$popIt.success('نجح', `تم إنشاء الفاتورة بنجاح برقم: ${res.data.name}`)
          // Redirect to the created invoice detail page
          this.$router.push(`/my-invoices/${res.data.name}`)
        } else {
          this.$popIt.error('خطأ', res.text || 'فشل في إنشاء الفاتورة')
        }
      } catch (error) {
        console.error('Error creating invoice:', error)
        const errorMessage = error.response?.data?.message || error.message || 'حدث خطأ أثناء إنشاء الفاتورة'
        this.$popIt.error('خطأ', errorMessage)
      } finally {
        this.loading = false
      }
    }
  },

  mounted() {
    this.fetchCustomers()
    this.fetchItems()
  }
}
</script>

<style scoped>
.content-wrapper {
  direction: rtl;
  text-align: right;
}

.table th, .table td {
  text-align: center;
}

.form-control {
  text-align: right;
}
</style>
