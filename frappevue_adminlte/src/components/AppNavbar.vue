<template>
  <div class="navbar bg-primary text-base-100">
    <div class="flex-1">
      <router-link class="btn btn-ghost normal-case text-xl" to="/">نظام إدارة المبيعات</router-link>
    </div>
    <div class="flex-none gap-2">
      <!-- Navigation Menu -->
      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-ghost">
          <i class="fas fa-file-invoice"></i> الفواتير
          <i class="fas fa-chevron-down ml-1"></i>
        </label>
        <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 text-base-content">
          <li><router-link to="/my-invoices"><i class="fas fa-list"></i> قائمة الفواتير</router-link></li>
          <li><router-link to="/create-invoice"><i class="fas fa-plus"></i> إنشاء فاتورة جديدة</router-link></li>
          <li><hr class="my-1"></li>
          <li><router-link to="/create-customer"><i class="fas fa-user-plus"></i> إضافة عميل جديد</router-link></li>
        </ul>
      </div>

      <div class="form-control">
        <input type="text" placeholder="بحث..." class="input input-bordered" v-model="query" @keyup.enter="goSearch" />
      </div>

      <div class="dropdown dropdown-end">
        <label tabindex="0" class="btn btn-ghost btn-circle avatar">
          <div class="w-10 rounded-full">
            <i class="fas fa-user-circle text-2xl"></i>
          </div>
        </label>
        <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 text-base-content">
          <li><router-link to="/profile"><i class="fas fa-user"></i> الملف الشخصي</router-link></li>
          <li><hr class="my-1"></li>
          <li><button @click="logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</button></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const query = ref('')
function goSearch() {
  if (query.value) {
    router.push({ name: 'DocList', params: { doctype: query.value } })
  }
}
function logout() {
  router.push('/login')
}
</script>
