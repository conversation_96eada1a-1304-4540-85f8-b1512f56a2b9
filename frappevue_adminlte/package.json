{"name": "frappe_vue", "version": "0.1.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "dev": "vite", "build:vite": "vite build", "preview": "vite preview"}, "dependencies": {"@leam-tech/renovation-core": "^1.2.4", "@tailwindcss/postcss7-compat": "^2.2.17", "@vue/devtools": "^5.3.4", "autoprefixer": "^9.8.8", "axios": "^1.6.2", "core-js": "^3.6.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "pinia": "^2.1.4", "postcss": "^7.0.39", "serve": "^13.0.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue": "^3.4.14", "vue-router": "^4.3.0", "vue-the-storages": "^1.0.2"}, "devDependencies": {"@headlessui/vue": "^1.7.18", "@vitejs/plugin-vue": "^5.0.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "daisyui": "^4.10.2", "vite": "^5.2.0"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"]}